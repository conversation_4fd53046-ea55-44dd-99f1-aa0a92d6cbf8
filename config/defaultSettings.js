/*
 * @Description:
 * @Author:
 * @Date: 2023-02-20 11:37:44
 * @LastEditTime: 2023-03-24 11:28:04
 * @LastEditors:
 */
module.exports = {
  navTheme: 'light',
  primaryColor: '#52c41a',
  layout: 'mix', // topmenu
  contentWidth: 'Fluid',
  fixedHeader: true,
  autoHideHeader: false,
  fixSiderbar: true,
  splitMenus: true,
  menu: {
    disableLocal: true,
  },
  title: '燕文物流',
  pwa: false,
  //at.alicdn.com/t/font_1601161_obr3yy9jq2.js
  iconfontUrl: '//at.alicdn.com/t/c/font_2596853_32ucwnyi4bs.js',
  collapse: true,
  consoleShow: true, // 是否打印console
  // portalUrl: 'https://portal-test.yanwentech.com/server',
  // 测试本地环境
  portalUrl: 'https://portal-test.yanwentech.com/csc',
  // portalUrl: 'http://127.0.0.1:8080/csc',
  // portalUrl: 'http://*************:8080/csc',
  weChatUrl: 'https://portal.yw56.com.cn/wechattest',
  shopifyUrl: 'https://shopify-test.yw56.com.cn',
  crmUrl: 'https://crm-test.yanwentech.com/crmapitest/api/download',
  overseaUrl: 'http://portal-overseatest.yanwentech.com:8001/login',
};
