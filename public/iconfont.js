!(function(i) {
  var c,
    o =
      '<svg><symbol id="actiongrid" viewBox="0 0 1024 1024"><path d="M490.666667 392.078222c0 54.442667-44.145778 98.588444-98.588444 98.588444L148.366222 490.666667c-54.442667 0-98.588444-44.145778-98.588444-98.588444L49.777778 148.366222C49.777778 93.923556 93.923556 49.777778 148.366222 49.777778l243.726222 0c54.442667 0 98.588444 44.145778 98.588444 98.588444L490.680889 392.078222zM433.777778 148.366222c0-23.025778-18.673778-41.699556-41.699556-41.699556L148.366222 106.666667C125.340444 106.666667 106.666667 125.340444 106.666667 148.366222l0 243.726222c0 23.025778 18.673778 41.699556 41.699556 41.699556l243.726222 0c23.025778 0 41.699556-18.673778 41.699556-41.699556L433.792 148.366222z"  ></path><path d="M163.555556 277.333333c-7.864889 0-14.222222-6.357333-14.222222-14.222222l0-25.628444c0-39.395556 26.282667-73.927111 62.421333-73.927111l88.604444 0c7.850667 0 14.222222 6.357333 14.222222 14.222222s-6.371556 14.222222-14.222222 14.222222l-88.604444 0c-22.186667 0-33.976889 24.462222-33.976889 45.482667L177.777778 263.111111C177.777778 270.976 171.406222 277.333333 163.555556 277.333333z"  ></path><path d="M158.136889 326.115556c-3.712 0-7.409778-1.564444-10.097778-4.124444-2.574222-2.688-4.124444-6.4-4.124444-10.097778s1.550222-7.395556 4.124444-10.097778c5.404444-5.262222 14.776889-5.262222 20.195556 0 2.56 2.702222 4.124444 6.4 4.124444 10.097778s-1.564444 7.409778-4.124444 9.955556C165.532444 324.536889 161.834667 326.115556 158.136889 326.115556z"  ></path><path d="M974.222222 392.078222c0 54.442667-44.145778 98.588444-98.588444 98.588444L631.921778 490.666667c-54.442667 0-98.588444-44.145778-98.588444-98.588444L533.333333 148.366222c0-54.442667 44.145778-98.588444 98.588444-98.588444l243.726222 0c54.442667 0 98.588444 44.145778 98.588444 98.588444L974.236444 392.078222zM917.333333 148.366222c0-23.025778-18.673778-41.699556-41.699556-41.699556L631.921778 106.666667c-23.025778 0-41.699556 18.673778-41.699556 41.699556l0 243.726222c0 23.025778 18.673778 41.699556 41.699556 41.699556l243.726222 0c23.025778 0 41.699556-18.673778 41.699556-41.699556L917.347556 148.366222z"  ></path><path d="M490.666667 875.633778c0 54.442667-44.145778 98.588444-98.588444 98.588444L148.366222 974.222222c-54.442667 0-98.588444-44.145778-98.588444-98.588444L49.777778 631.921778c0-54.442667 44.145778-98.588444 98.588444-98.588444l243.726222 0c54.442667 0 98.588444 44.145778 98.588444 98.588444L490.680889 875.633778zM433.777778 631.921778c0-23.025778-18.673778-41.699556-41.699556-41.699556L148.366222 590.222222c-23.025778 0-41.699556 18.673778-41.699556 41.699556l0 243.726222c0 23.025778 18.673778 41.699556 41.699556 41.699556l243.726222 0c23.025778 0 41.699556-18.673778 41.699556-41.699556L433.792 631.921778z"  ></path><path d="M974.222222 875.633778c0 54.442667-44.145778 98.588444-98.588444 98.588444L631.921778 974.222222c-54.442667 0-98.588444-44.145778-98.588444-98.588444L533.333333 631.921778c0-54.442667 44.145778-98.588444 98.588444-98.588444l243.726222 0c54.442667 0 98.588444 44.145778 98.588444 98.588444L974.236444 875.633778zM917.333333 631.921778c0-23.025778-18.673778-41.699556-41.699556-41.699556L631.921778 590.222222c-23.025778 0-41.699556 18.673778-41.699556 41.699556l0 243.726222c0 23.025778 18.673778 41.699556 41.699556 41.699556l243.726222 0c23.025778 0 41.699556-18.673778 41.699556-41.699556L917.347556 631.921778z"  ></path></symbol><symbol id="actionsettings" viewBox="0 0 1024 1024"><path d="M934.229333 438.4c-2.986667-3.882667-6.869333-6.186667-11.690667-6.954667L820.906667 415.914667c-5.589333-17.792-13.184-35.925333-22.784-54.442667 6.656-9.258667 16.64-22.314667 29.994667-39.168 13.312-16.853333 22.784-29.184 28.330667-36.949333 2.944-4.096 4.437333-8.32 4.437333-12.8 0-5.162667-1.28-9.258667-3.882667-12.202667-13.354667-18.901333-43.904-50.389333-91.690667-94.464-4.437333-3.669333-9.045333-5.546667-13.866667-5.546667-5.546667 0-9.984 1.664-13.354667 4.992l-78.848 59.477333c-15.189333-7.808-31.872-14.634667-50.005333-20.565333l-15.573333-102.229333c-0.384-4.821333-2.517333-8.789333-6.4-11.946667S578.858667 85.333333 573.653333 85.333333l-123.306667 0c-10.752 0-17.408 5.205333-20.010667 15.573333C425.514667 119.424 420.138667 153.856 414.208 204.245333 396.8 209.792 379.946667 216.832 363.648 225.365333L287.018667 165.888c-4.821333-3.669333-9.642667-5.546667-14.464-5.546667-8.149333 0-25.642667 13.269333-52.48 39.722667-26.88 26.496-45.098667 46.421333-54.741333 59.733333C162.005333 264.618667 160.341333 268.885333 160.341333 272.554667c0 4.48 1.834667 8.917333 5.546667 13.354667C190.72 315.904 210.517333 341.461333 225.322667 362.581333 216.064 379.605333 208.853333 396.629333 203.648 413.696L100.352 429.226667c-4.096 0.768-7.594667 3.157333-10.581333 7.253333C86.826667 440.533333 85.333333 444.8 85.333333 449.237333l0 123.349333c0 4.821333 1.493333 9.173333 4.437333 13.056 2.986667 3.882667 6.869333 6.186667 11.690667 6.912L203.093333 607.573333c5.205333 18.133333 12.8 36.48 22.784 54.997333-6.656 9.258667-16.64 22.314667-29.994667 39.168-13.312 16.853333-22.784 29.184-28.330667 36.949333-2.944 4.053333-4.437333 8.32-4.437333 12.757333 0 4.821333 1.28 9.088 3.882667 12.8 14.464 20.010667 45.013333 51.114667 91.648 93.312 4.096 4.096 8.704 6.144 13.909333 6.144 5.546667 0 10.197333-1.664 13.909333-5.034667l78.293333-59.434667c15.189333 7.808 31.872 14.634667 50.005333 20.565333l15.573333 102.229333c0.384 4.821333 2.517333 8.789333 6.4 11.946667 3.882667 3.157333 8.405333 4.693333 13.610667 4.693333l123.306667 0c10.752 0 17.408-5.162667 20.010667-15.530667 4.821333-18.517333 10.197333-52.949333 16.128-103.338667 17.408-5.546667 34.261333-12.586667 50.56-21.12l76.629333 59.989333c5.205333 3.370667 10.026667 5.034667 14.464 5.034667 8.149333 0 25.557333-13.184 52.224-39.466667 26.666667-26.282667 45.013333-46.293333 54.997333-59.989333 3.328-3.712 4.992-7.978667 4.992-12.8 0-4.778667-1.834667-9.429333-5.546667-13.866667-26.666667-32.597333-46.464-58.154667-59.434667-76.672 7.381333-13.696 14.634667-30.549333 21.674667-50.56l102.741333-15.530667c4.48-0.768 8.149333-3.157333 11.136-7.253333C937.173333 583.509333 938.666667 579.242667 938.666667 574.805333l0-123.349333C938.666667 446.634667 937.173333 442.282667 934.229333 438.4zM896 555.690667l-107.434667 16.554667c0 0-18.048 55.722667-39.125333 90.88 21.589333 32.597333 64.725333 87.808 64.725333 87.808s-55.210667 61.738667-63.232 63.786667c-15.573333-11.562667-86.357333-66.773333-86.357333-66.773333s-56.704 32.64-93.866667 39.168c-2.005333 21.546667-13.013333 102.869333-15.018667 108.885333-17.066667 0.512-85.845333 0-85.845333 0l-16.554667-108.416c0 0-59.733333-17.578667-93.397333-39.125333-40.149333 31.104-85.802667 66.261333-85.802667 66.261333L209.834667 750.933333c0 0 52.693333-65.237333 65.749333-84.821333C264.021333 644.522667 257.024 640 236.416 568.746667c-63.744-8.533333-108.928-15.573333-108.928-15.573333l0-85.333333 108.928-16.042667c0 0 15.573333-58.752 39.168-91.861333C249.472 323.285333 209.834667 271.573333 209.834667 271.573333s53.674667-57.728 64.256-62.72c11.008 9.002667 84.821333 66.261333 84.821333 66.261333s54.698667-28.629333 94.378667-38.656C456.789333 205.824 466.304 135.552 468.309333 128l85.333333 0 16.597333 107.434667c0 0 80.042667 27.349333 92.330667 39.68 21.12-15.061333 87.850667-65.792 87.850667-65.792s49.194667 47.701333 61.738667 63.274667c-12.544 17.536-64.725333 86.314667-64.725333 86.314667s28.586667 46.208 40.661333 94.378667C829.738667 460.8 896 470.869333 896 470.869333L896 555.690667z"  ></path><path d="M511.744 326.442667c-102.528 0-185.685333 83.114667-185.685333 185.685333 0 102.528 83.157333 185.642667 185.685333 185.642667 102.528 0 185.685333-83.114667 185.685333-185.642667C697.429333 409.557333 614.272 326.442667 511.744 326.442667zM511.744 654.08c-78.421333 0-141.952-63.573333-141.952-141.952 0-78.421333 63.530667-141.994667 141.952-141.994667 78.421333 0 141.952 63.573333 141.952 141.994667C653.696 590.506667 590.165333 654.08 511.744 654.08z"  ></path></symbol><symbol id="actiondoc" viewBox="0 0 1024 1024"><path d="M560 0H208c-35.344 0-64 28.656-64 64v896c0 35.344 28.656 64 64 64h608c35.344 0 64-28.656 64-64V320.016L560 0z m256 346.528V352H528V64h5.504L816 346.528zM208 960V64h256v352h352v544H208z"  ></path></symbol><symbol id="actionusers" viewBox="0 0 1024 1024"><path d="M745.266378 834.970076l-201.273871-111.490251c74.806362-58.854066 95.122364-174.516211 95.122364-239.364376V349.128327c0-89.384013-118.763093-188.934018-238.053666-188.934018-119.258605 0-241.170598 99.56599-241.170597 188.934018v134.987122c0 58.965955 24.951438 178.256529 100.525045 238.852879L53.946896 834.970076S0 859.010412 0 888.980909v81.008258c0 29.810655 24.200178 54.010833 53.946896 54.010833h691.319482c29.778687 0 53.978865-24.200178 53.978865-54.010833v-81.008258c0-31.77672-53.978865-54.010833-53.978865-54.010833z m-9.958197 125.092862H63.937062V902.903205c4.587484-3.340711 10.997175-7.208904 16.048203-9.526623 1.502521-0.687323 3.005042-1.406615 4.427641-2.221812l206.484742-111.985764c18.829465-10.213946 31.313176-29.1553 33.215303-50.478311s-7.001108-42.214445-23.688681-55.60926c-53.611226-42.98169-76.580616-138.183975-76.580616-188.965986V349.128327c0-45.922795 86.570782-124.996956 177.233536-124.996956 92.341102 0 174.116604 77.987231 174.116604 124.996956v134.987122c0 50.078704-15.55269 145.69658-70.714391 189.093861a64.036964 64.036964 0 0 0-24.200178 55.609259 64.04096 64.04096 0 0 0 33.08743 50.797996l201.273871 111.490252c1.774253 0.97504 4.028035 2.030002 5.898194 2.845199 4.715358 1.998033 10.517647 5.338745 14.769461 8.27985v57.831072z m233.705946-284.280162l-204.262929-111.490251c74.806362-58.854066 98.127406-174.516211 98.127406-239.364376v-134.987122c0-89.384013-121.75215-189.941027-241.042724-189.941027-77.523688 0-156.78966 42.150508-202.92025 96.129373 26.310101 1.630395 55.321543 1.662364 80.384871 9.654496 33.678847-26.23018 76.292899-41.830823 122.535379-41.830823 92.341102 0 177.105662 78.978256 177.105662 126.003965v134.987122c0 50.078704-18.541748 145.69658-73.703448 189.093861a64.036964 64.036964 0 0 0-24.200178 55.609259 64.04096 64.04096 0 0 0 33.087429 50.797996l204.262929 111.490252c1.774253 0.97504 4.028035 2.030002 5.898194 2.845199 4.715358 1.998033 10.517647 5.338745 14.769461 8.27985v56.856032H829.99897c19.48482 14.705524 25.383014 35.740818 32.943571 63.937062h106.08757c29.778687 0 53.978865-24.200178 53.978865-54.010833v-80.017233c-0.015984-31.808688-53.994849-54.042802-53.994849-54.042802z"  ></path></symbol><symbol id="actionflag" viewBox="0 0 1024 1024"><path d="M878.368 97.728a30.624 30.624 0 0 0-33.312 6.24c-0.352 0.352-37.152 35.296-95.168 56.064-75.456 27.008-150.752 18.4-223.808-25.504-38.336-23.072-81.888-40.672-129.376-52.32a608.224 608.224 0 0 0-119.36-16.512c-70.816-2.912-121.504 6.24-123.616 6.624a30.72 30.72 0 0 0-25.152 30.208V927.36a30.72 30.72 0 0 0 61.376 0v-297.984c15.552-5.248 48-14.432 91.392-17.472a425.376 425.376 0 0 1 108.352 6.368c44.832 8.416 89.056 24.096 131.424 46.624 46.272 24.576 93.792 41.056 141.216 48.992 38.272 6.368 76.544 7.232 113.728 2.496 64.288-8.16 103.872-30.464 105.536-31.392a30.72 30.72 0 0 0 15.456-26.656V126.176a30.72 30.72 0 0 0-18.72-28.32z m-42.688 540.768a282.496 282.496 0 0 1-70.304 17.216c-30.976 3.648-62.944 2.688-94.944-2.848-40.192-6.944-80.736-21.184-120.48-42.304a521.952 521.952 0 0 0-150.848-53.088 489.92 489.92 0 0 0-125.024-6.656c-34.88 2.656-63.616 8.736-84.096 14.368V129.472a609.184 609.184 0 0 1 86.4-2.336c58.176 2.56 143.264 15.04 218.08 60.032 61.312 36.864 119.2 49.504 169.696 49.472a317.12 317.12 0 0 0 109.024-19.712 361.12 361.12 0 0 0 62.528-30.176v451.776z"  ></path></symbol></svg>',
    t = (c = document.getElementsByTagName('script'))[c.length - 1].getAttribute('data-injectcss');
  if (t && !i.__iconfont__svg__cssinject__) {
    i.__iconfont__svg__cssinject__ = !0;
    try {
      document.write(
        '<style>.svgfont {display: inline-block;width: 1em;height: 1em;fill: currentColor;vertical-align: -0.1em;font-size:16px;}</style>'
      );
    } catch (c) {
      console && console.log(c);
    }
  }
  !(function(c) {
    if (document.addEventListener)
      if (~['complete', 'loaded', 'interactive'].indexOf(document.readyState)) setTimeout(c, 0);
      else {
        var t = function() {
          document.removeEventListener('DOMContentLoaded', t, !1), c();
        };
        document.addEventListener('DOMContentLoaded', t, !1);
      }
    else
      document.attachEvent &&
        ((l = c),
        (a = i.document),
        (n = !1),
        (o = function() {
          try {
            a.documentElement.doScroll('left');
          } catch (c) {
            return void setTimeout(o, 50);
          }
          e();
        })(),
        (a.onreadystatechange = function() {
          'complete' == a.readyState && ((a.onreadystatechange = null), e());
        }));
    function e() {
      n || ((n = !0), l());
    }
    var l, a, n, o;
  })(function() {
    var c, t, e, l, a, n;
    ((c = document.createElement('div')).innerHTML = o),
      (o = null),
      (t = c.getElementsByTagName('svg')[0]) &&
        (t.setAttribute('aria-hidden', 'true'),
        (t.style.position = 'absolute'),
        (t.style.width = 0),
        (t.style.height = 0),
        (t.style.overflow = 'hidden'),
        (e = t),
        (l = document.body).firstChild
          ? ((a = e), (n = l.firstChild).parentNode.insertBefore(a, n))
          : l.appendChild(e));
  });
})(window);
