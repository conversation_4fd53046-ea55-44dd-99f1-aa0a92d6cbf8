import React, { Component } from 'react';
import { ExclamationCircleFilled } from '@ant-design/icons';
import {
  Card,
  Avatar,
  Row,
  Col,
  Button,
  Modal,
  Message,
  List,
  Popover,
  Typography,
  Tooltip,
  Popconfirm,
  message,
  Space,
} from 'antd';
import { connect } from 'dva';
import { Redirect, router } from 'umi';
import moment from 'moment';
import Slider from 'react-slick';
import styles from './homePage.less';
import avatar from '../../assets/avatar.jpg';
import shippericon from '../../assets/shippericon.png';
import infoicon from '../../assets/infoicon.png';
// eslint-disable-next-line camelcase
import account_icon1 from '../../assets/account_icon1.png';
// eslint-disable-next-line camelcase
import account_icon2 from '../../assets/account_icon2.png';
// eslint-disable-next-line camelcase
import account_icon3 from '../../assets/account_icon3.png';
// eslint-disable-next-line camelcase
import account_icon4 from '../../assets/account_icon4.png';
import { isAuth } from '@/utils/utils';
import noBill from '../../assets/merchant/noBill.png';
import Exception from '@/components/Exception';
import { BillingServiceModal } from '@/pages/HomePage/components/billingServiceModal';
import { formatMessage } from 'umi/locale';
import MerchantStatusModal from './components/merchantStatusModal';

const { Paragraph } = Typography;

@connect(({ homePage, global, user, loading }) => ({
  homePage,
  global,
  user,
  loading: loading.effects['homePage/getHomePageMerchantInfo'],
  shipperLoading: loading.effects['homePage/getHomePageShippers'],
  authorizedLoading: loading.effects['user/qryMerchantState'],
  getBillLoading: loading.effects['homePage/getHomePageBillInfo'],
  notificationsLoading: loading.effects['homePage/getNotifications'],
  queryNewsLoading: loading.effects['homePage/getQueryNews'],
  unFreeMerchantLoading: loading.effects['homePage/unFreezeMerchant'],
}))
class HomePage extends Component {
  constructor(props) {
    super(props);
  }

  state = {
    shipperArray: [],
    news: [],
    notification: [],
    merchantBillInfo: {},
    visible: false,
    quotationData: [],
    merchantInfo: {},
    // 商户状态
    currentAuthorityValue: null,
    // 是否显示预付款
    isShowFrozenBalance: '',
    detalsmyBill: '',
    noticeCount: 0,
    allMessageVisible: false, // 所有消息弹窗合并
    allMessageArray: [], // 所有消息对象
    merchantStatusVisible: false, // 商户冻结弹窗
  };

  componentWillUnmount() {
    Modal.destroyAll();
  }

  componentDidMount() {
    const { dispatch } = this.props;

    dispatch({
      type: 'user/qryMerchantState',
      callback: (response, authorityValue) => {
        if (response.success) {
          if (localStorage.getItem('isHomeNoticeModal') === 'true') {
            this.getAllModalMessage();
          } else {
            this.getBillServiceConfirmSignStatus();
          }
          // dispatch({
          //   type: 'user/doYouNeedToShow',
          //   payload: response.data.state.userId,
          //   callback: result => {
          //     if (result.success) {
          //       dispatch({
          //         type: 'global/changeTourOpenState',
          //         payload: true,
          //       });
          //     } else {

          //     }
          //   },
          // });
          this.setState({
            currentAuthorityValue: authorityValue,
          });
          this.getIMParam();
          if (authorityValue === 'merchant') {
            this.getAllRequest();
          }
        } else {
          // 跳转500，请刷新--response.success=false,data.indexState=500
          router.push(`/exception/500`);
        }
      },
    });
  }

  getSnapshotBeforeUpdate(prevProps, prevState) {
    if (prevProps.global.isTourOpen !== this.props.global.isTourOpen) {
      return true;
    }
    return null;
  }

  componentDidUpdate(prevProps, prevState, snapshot) {
    if (snapshot && !this.props.global.isTourOpen) {
      if (localStorage.getItem('isHomeNoticeModal') === 'true') {
        this.getAllModalMessage();
      } else {
        this.getBillServiceConfirmSignStatus();
      }
    }
  }

  // 判断是否需要签署服务确认书
  getBillServiceConfirmSignStatus = () => {
    const { dispatch } = this.props;
    dispatch({
      type: 'homePage/getBillServiceConfirmSignStatus',
      callback: response => {
        if (response.success) {
          if (response.status == 1) {
            const result = localStorage.getItem('isShowBillService');
            // console.log(`result`, result);
            dispatch({
              type: 'homePage/changeBillService',
              payload: result != 1,
            });
          } else {
            localStorage.setItem('isShowBillService', '0');
          }
        }
      },
    });
  };

  // 获取计费服务确认书签署地址
  getBillServiceConfirmSignUrl = () => {
    const { dispatch } = this.props;
    dispatch({
      type: 'homePage/getBillServiceConfirmSignUrl',
      callback: response => {
        if (response.success) {
          localStorage.setItem('isShowBillService', '0');
          window.location.href = response.data;
        } else {
          message.error(response.status, 1);
        }
      },
    });
  };

  // 获取im客户数据
  getIMParam = () => {
    const { dispatch } = this.props;
    dispatch({
      type: 'homePage/getIMParam',
      callback: result => {
        if (result.success) {
          let Merchants = localStorage.getItem('Merchants');
          // 商户信息不一致，清除EJF订单查询缓存所需的数据
          if (Merchants && Merchants != result.data.c_cf_商户号) {
            localStorage.removeItem('productData');
            localStorage.removeItem('countriesData');
            localStorage.removeItem('tableData');
            localStorage.removeItem('deliveryAccount');
            localStorage.removeItem('miscellaneous');
            localStorage.removeItem('number');
          }
          localStorage.setItem('Merchants', result.data.c_cf_商户号);
        }
      },
    });
  };

  // 合并弹窗信息
  getAllModalMessage = () => {
    const { dispatch } = this.props;
    dispatch({
      type: 'user/getUserInfo',
      callback: result => {
        if (result.success) {
          this.setState({
            userName: result.data.userName,
          });
          // 获取异常件数量
          dispatch({
            type: 'abnormal/fetchShippers',
            payload: { type: 0 },
            callback: response => {
              const shipperss = response.data;
              const zhs = [];
              for (let i = 0; i < shipperss.length; i++) {
                zhs.push(shipperss[i].customerCode);
              }
              // 获取列表
              // 新增接口
              dispatch({
                type: 'homePage/popupMerge',
                payload: {
                  ShippingAccounts: zhs,
                  SourceIds: [0, 4, 5, 6, 8, 9, 10, 12, 18, 25],
                  IsExport: false,
                  Status: 0,
                  PageIndex: 1,
                  PageSize: 50,
                },
                callback: responseValue => {
                  let data = [];
                  let visible = false;

                  if (responseValue.success) {
                    if (responseValue.data.length > 0) {
                      data = responseValue.data.map((item, index) => {
                        if (item.hasOwnProperty('announcement')) {
                          //当前为公告
                          item.auditName = item.announcement.auditName;
                          item.auditUserId = item.announcement.auditUserId;
                          item.author = item.announcement.author;
                          item.categoryId = item.announcement.categoryId;
                          item.contentId = item.announcement.contentId;
                          item.inputdate = item.announcement.inputdate;
                          item.keywords = item.announcement.keywords;
                          item.modelId = item.announcement.modelId;
                          item.recommend = item.announcement.recommend;
                          item.siteId = item.announcement.siteId;
                          item.thumb = item.announcement.thumb;
                          item.top = item.announcement.top;
                          item.updatedate = item.announcement.updatedate;
                          item.url = item.announcement.url;
                          item.viewNum = item.announcement.viewNum;
                          item.titleHeader = '公告通知';
                          item.buttonTitle = '查看详情';
                          item.text = `更新时间: ${item.updatedate}`;
                          item.description = item.announcement.title;
                        } else if (
                          item.hasOwnProperty('abnormal') ||
                          item.hasOwnProperty('outsideAbnormaNum')
                        ) {
                          // 当前为异常件
                          item.titleHeader = '异常件提醒';
                          item.buttonTitle = '现在处理';
                          item.text =
                            item?.abnormal && item?.outsideAbnormaNum
                              ? `待处理-问题件: ${item.abnormal ||
                                  0}、仓外异常件：${item.outsideAbnormaNum || 0}`
                              : item?.abnormal
                              ? `待处理-问题件: ${item.abnormal || 0}`
                              : `待处理-仓外异常件：${item.outsideAbnormaNum || 0}`;
                          item.description = '您好，请及时处理异常件，以免影响货物整体时效。';
                        } else if (item.hasOwnProperty('certificateValidityPeriod')) {
                          if (item.certificateValidityPeriod == 1) {
                            // 更新证件
                            item.titleHeader = '证件有效期提醒';
                            item.buttonTitle = '更新证件';
                            item.text = `证件有效截止日: ${moment(item.data).format(
                              'YYYY年MM月DD日'
                            )}`;
                            item.description = '请在有效截止日前上传新的有效证件。';
                          } else if (item.certificateValidityPeriod == 2) {
                            // 更新资料
                            item.titleHeader = '更新资料提醒';
                            item.buttonTitle = '更新资料';
                            item.text = `证件有效截止日: ${moment(item.data).format(
                              'YYYY年MM月DD日'
                            )}`;
                            item.description = '请在有效截止日前上传新的有效证件。';
                          }
                        } else if (item.hasOwnProperty('orderNum')) {
                          // 到达专线代取提醒
                          item.titleHeader = '待领取件提醒';
                          item.buttonTitle = '现在处理';
                          item.text = `近三月国外投递异常运单件数: ${item.orderNum}`;
                          item.description =
                            '您好，请及时查看到达待取运单并通知收件人，避免退件产生损失。';
                        } else if (
                          item.hasOwnProperty('enterpriseWeChat') &&
                          item.enterpriseWeChat
                        ) {
                          // 联系方式变更
                          item.titleHeader = '联系方式变更提醒';
                          item.buttonTitle = '立即添加';
                          item.text = '您的销售或客服的沟通工具已变更成企业微信';
                          item.description = '';
                        } else if (item.hasOwnProperty('isNeedModifyPwd') && item.isNeedModifyPwd) {
                          // 密码修改
                          item.titleHeader = '密码修改提醒';
                          item.buttonTitle = '修改密码';
                          item.text =
                            '系统检测到您已长时间未修改密码，为了您的使用安全，建议您点击【修改密码】，重新设置一个9-20位的密码（含字母、数字或符号，符号可以有_.!@#?）';
                          item.description = '';
                        }
                        item.showUpdateModal = false;
                        return item;
                      });
                      if (data.findIndex(item => item.hasOwnProperty('titleHeader')) == -1) {
                        // 说明只有一条消息是联系方式的
                        data = [];
                      } else {
                        // 说明有弹窗消息去掉联系方式
                        let findIndex = data.findIndex(item =>
                          item.hasOwnProperty('enterpriseWeChat')
                        );
                        if (!data[findIndex]?.enterpriseWeChat) {
                          data.splice(findIndex, 1);
                        }
                        let findIndexPwd = data.findIndex(item =>
                          item.hasOwnProperty('isNeedModifyPwd')
                        );
                        if (!data[findIndexPwd]?.isNeedModifyPwd) {
                          data.splice(findIndexPwd, 1);
                        }
                      }
                      if (localStorage.getItem('clickError') === 'true') {
                        // 点击了异常件
                        let errorIndex = data.findIndex(item => item.titleHeader === '异常件提醒');
                        data.splice(errorIndex, 1);
                        if (data.length > 0) {
                          visible = true;
                        } else {
                          data = [];
                          visible = false;
                          localStorage.setItem('isHomeNoticeModal', 'false');
                          localStorage.setItem('clickError', 'false');
                          localStorage.setItem('clickIdCard', 'false');
                        }
                      } else if (localStorage.getItem('clickIdCard') === 'true') {
                        // 点击了证件更新
                        let idCardIndex = data.findIndex(
                          item =>
                            item.titleHeader === '更新资料提醒' ||
                            item.titleHeader === '证件有效期提醒'
                        );
                        data.splice(idCardIndex, 1);
                        if (data.length > 0) {
                          visible = true;
                        } else {
                          data = [];
                          visible = false;
                          localStorage.setItem('isHomeNoticeModal', 'false');
                          localStorage.setItem('clickError', 'false');
                          localStorage.setItem('clickIdCard', 'false');
                        }
                      } else {
                        visible = data.length > 0;
                        if (!visible) {
                          this.getBillServiceConfirmSignStatus();
                        }
                      }
                    } else {
                      data = [];
                      visible = false;
                      localStorage.setItem('isHomeNoticeModal', 'false');
                      localStorage.setItem('clickError', 'false');
                      localStorage.setItem('clickIdCard', 'false');
                      this.getBillServiceConfirmSignStatus();
                    }

                    this.setState({
                      allMessageVisible: visible,
                      allMessageArray: data,
                    });
                  } else {
                    this.getBillServiceConfirmSignStatus();
                  }
                },
              });
            },
          });
        }
      },
    });
  };

  // 企业微信点击请求接口
  wechatRequest = () => {
    const { dispatch } = this.props;
    dispatch({
      type: 'homePage/enterpriseWeChat',
      callback: result => {
        if (result.success) {
          router.push('/merchant/shipperInfoList');
        } else {
          Message.error(result.message);
        }
      },
    });
  };

  clickItem = item => {
    const { dispatch } = this.props;
    const { allMessageArray } = this.state;
    switch (item.titleHeader) {
      case '公告通知': // 查看公告
        dispatch({
          type: 'homePage/readAnnouncement',
          payload: {
            announcementId: item.contentId,
          },
          callback: result => {
            if (result.success) {
              let param = {
                order: 'asc',
                limit: 10,
                currentPage: 1,
                context: '',
                categoryId: '196',
                top: null,
              };
              // 进入详情
              router.push({
                pathname: '/homePage/notificationMessage',
                state: { contentId: item.contentId, conList: [{}], param: param },
              });
            } else {
              Message.error(result.message);
            }
          },
        });
        break;
      case '异常件提醒': // 异常件
        localStorage.setItem('clickError', 'true');
        if (item?.abnormal) {
          router.push('/express/abnormalList');
        } else {
          router.push(`/express/abnormalList?type=2`);
        }
        break;
      case '证件有效期提醒':
      case '更新资料提醒':
        let array = [];
        array = allMessageArray.map(value => {
          if (value.code === item.code) {
            value.showUpdateModal = true;
          }
          return value;
        });
        this.setState({
          allMessageArray: array,
        });
        break;
      case '待领取件提醒':
        router.push('/express/specialLineTrackingList');
        break;
      case '联系方式变更提醒':
        this.wechatRequest();
        break;
      case '密码修改提醒':
        router.push('/merchant/changePassword');
        break;
    }
  };
  // 更新资料和更新证件
  handleOk = item => {
    const { dispatch } = this.props;
    this.cancelPopconfirm(item);
    dispatch({
      type: 'homePage/updateMerchantStatus',
      payload: {
        type: item.certificateValidityPeriod + '',
      },
      callback: result => {
        if (result.success) {
          if (item.certificateValidityPeriod == 1) {
            router.push(`/merchant/merchantUpdateIdentification`);
          }
          if (item.certificateValidityPeriod == 2) {
            router.push(`/merchant/personalMerchantInfoUpdate`);
          }
          localStorage.setItem('clickIdCard', 'true');
        } else {
          message.warning(result.message, 1);
        }
      },
    });
  };

  // 点击取消时
  handleMessageCancel = item => {
    this.cancelPopconfirm(item);
  };
  // 消失气泡提示框公用方法
  cancelPopconfirm = item => {
    const { allMessageArray } = this.state;
    let array = [];
    array = allMessageArray.map(value => {
      if (value.code === item.code) {
        value.showUpdateModal = false;
      }
      return value;
    });
    this.setState({
      allMessageArray: array,
    });
  };

  // 获取未读消息条数
  getUnReadNoticeCount = () => {
    const { dispatch } = this.props;
    dispatch({
      type: 'homePage/getUnReadNoticeCount',
      payload: { siteId: 1, categoryId: 196 },
      callback: result => {
        if (result.success) {
          let top =
            result.data.top === null || result.data.top === undefined
              ? 0
              : parseInt(result.data.top);
          let normal =
            result.data.normal === null || result.data.normal === undefined
              ? 0
              : parseInt(result.data.normal);
          this.setState({
            noticeCount: top + normal,
          });
        }
      },
    });
  };

  // 需要在Did页面调用的请求
  getAllRequest = () => {
    const { dispatch } = this.props;
    dispatch({
      type: 'homePage/getHomePageMerchantInfo',
      callback: response => {
        if (response.success) {
          this.setState({
            isShowFrozenBalance: response.data.merchant.payCycleName,
            merchantInfo: response.data,
          });
        } else {
          this.setState({
            merchantInfo: null,
          });
        }
      },
    });
    dispatch({
      type: 'homePage/getHomePageBillInfo',
      callback: response => {
        const data = response.data;
        if (response.success) {
          this.setState({
            merchantBillInfo: data.merchantInfo,
            detalsmyBill: data.myBill,
          });
        } else {
          this.setState({
            merchantBillInfo: undefined,
          });
        }
      },
    });
    dispatch({
      type: 'homePage/getHomePageShippers',
      callback: data => {
        if (data != null) {
          this.setState({
            shipperArray: data,
          });
        }
      },
    });
    dispatch({
      type: 'homePage/getHomePageNoticeList',
      payload: { siteId: 1, categoryId: 196 },
      callback: response => {
        if (response.success) {
          if (response.data != null) {
            const IDlist = [];
            response.data.map(item => {
              IDlist.push(item.contentId);
              item.updatedate = moment(item.updatedate).format('YYYY.MM.DD');
              item.inputdate = moment(item.inputdate).format('YYYY.MM.DD');
            });
            this.setState({
              notification: response.data,
              contentIdList: IDlist,
            });
          }
        } else {
          this.setState({
            notification: [],
          });
        }
      },
    });
    dispatch({
      type: 'homePage/getQueryNews',
      callback: response => {
        if (response.success) {
          if (response.data != null) {
            this.setState({
              news: response.data,
            });
          }
        } else {
          this.setState({
            news: null,
          });
        }
      },
    });
    this.getUnReadNoticeCount();
  };

  // 跳转账单
  billDetail = () => {
    router.push(`/myBill/billList`);
  };

  enterOverwareAction = value => {
    const { dispatch } = this.props;
    dispatch({
      type:
        value.accountType === 2
          ? 'homePage/getEnterOverseaUrl'
          : 'homePage/getEnterChinaOverseaUrl',
      payload: {
        customerCode: value.customerCode,
      },
      callback: response => {
        if (response && response.success) {
          if (response.success) {
            if (response.status === '0') {
              this.getAuthStatus({ contractType: 4 });
            } else {
              window.open(response.data);
            }
          } else {
            message.error(
              `${value.accountType === 2 ? response.message : '中国仓服务暂时不可用'}`,
              2
            );
          }
        } else {
          // 资料完整度判断
          if (response.status == '0') {
            message.warn(response.message);
            router.push('/merchant/merchantInfoList?visible=1');
          } else {
            message.error(
              `${value.accountType === 2 ? response.message : '中国仓服务暂时不可用'}`,
              2
            );
          }
        }
      },
    });
  };

  getAuthStatus = param => {
    const { dispatch } = this.props;
    dispatch({
      type: 'Auth/getAuthStatus',
      payload: param,
      callback: result => {
        if (result.success) {
          router.push(result.data);
        } else {
          message.error(result.message);
        }
      },
    });
  };

  info = (
    IconStr,
    borderColor,
    bcgColor,
    title,
    value,
    bordered,
    isDetail,
    weekPay,
    isIcon,
    url
  ) => {
    const { merchantInfo } = this.state;
    return (
      <div
        className={styles.headerInfo}
        style={{ borderRight: !bordered ? 'none' : '1px dashed rgb(223, 227, 230)' }}
      >
        <Row>
          <Col span={isDetail ? 11 : 11} style={{ textAlign: 'center' }}>
            <div className={styles.iconDiv} style={{ borderColor, backgroundColor: bcgColor }}>
              <img src={IconStr} />
            </div>
          </Col>
          <Col span={isDetail ? 13 : 13}>
            <div style={{ fontSize: 14, color: '#888888', marginTop: 10 }}>
              <span>
                {title}&nbsp;
                <Tooltip
                  placement="top"
                  title={
                    title === '账户余额'
                      ? '代表已扣除已出账单部分的余款，正数代表有余款，负数代表已发生欠款'
                      : title === '未出账单金额'
                      ? '代表当周所发货物，处理后产生的金额；'
                      : title === '制单预扣金额'
                      ? '代表在制单系统成功下单时预扣的金额'
                      : title === '可用余额' && weekPay == '预付款'
                      ? '代表账户的当前可用余款，已经扣除未出账单金额和制单预扣金额，正数代表有余款，负数代表已经发生欠款'
                      : title === '可用余额'
                      ? '代表账户的当前可用余款，已经扣除未出账单金额，正数代表有余款，负数代表已经发生欠款'
                      : null
                  }
                >
                  <ExclamationCircleFilled
                    style={{ color: '#c0ccda', fontSize: 12, display: isIcon }}
                  />
                </Tooltip>
              </span>

              <Button
                type="link"
                onClick={this.billDetail}
                style={{ display: isDetail ? '' : 'none', height: 20.67 }}
              >
                详情
              </Button>
            </div>
            <div style={{ color: '#333333', fontSize: 20 }}>
              <span
                style={{ cursor: title === '可用余额' ? 'default' : 'pointer' }}
                onClick={() => {
                  if (url) {
                    router.push(url);
                  }
                }}
              >
                {value}
              </span>
              &nbsp;
              <Tooltip title="系统数据异常，请联系销售或者客服，或者点右下角的在线咨询！">
                <img
                  src={infoicon}
                  style={{
                    width: '18px',
                    display: title === '账户余额' && value === 'NULL' ? '' : 'none',
                  }}
                />
              </Tooltip>
            </div>
            <Button
              type="link"
              onClick={() => {
                router.push(`/myBill/unfreeze/unfreezeApply`);
              }}
              style={{
                display:
                  isDetail &&
                  merchantInfo !== null &&
                  merchantInfo.freeze !== undefined &&
                  merchantInfo.freeze.amountfreeze !== null &&
                  merchantInfo.freeze.amountfreeze === '1'
                    ? ''
                    : 'none',
                height: 20.67,
                paddingLeft: '0px',
              }}
            >
              申请解冻
            </Button>
          </Col>
          {bordered && <em />}
        </Row>
      </div>
    );
  };

  addUserActionLog = (id, type) => {
    const { dispatch } = this.props;
    dispatch({
      type: 'user/addUserActionLog',
      payload: { id: id, type: type },
    });
  };

  enterEjfACtion = value => {
    if (
      value &&
      (value.customerCode == '102759' ||
        value.customerCode == '********' ||
        value.customerCode == '12024941')
    ) {
      const { dispatch } = this.props;
      dispatch({
        type: 'homePage/enterEjf',
        payload: {
          customerCode: value.customerCode,
        },
        callback: response => {
          if (response && response.success) {
            if (response.success) {
              window.open(response.data);
            } else {
              Message.error('EJF服务暂时不可用', 2);
            }
          } else {
            Message.error('EJF服务暂时不可用', 2);
          }
        },
      });
    } else {
      router.push(`/express/createOrder?userId=${value.customerCode}`);
    }
  };

  showModal = value => {
    const { dispatch } = this.props;
    dispatch({
      type: 'homePage/getQuotationList',
      payload: {
        warehouseId: value.pickWarehouseId,
      },
      callback: data => {
        if (data !== undefined && data !== null) {
          this.setState({
            quotationData: data,
            visible: true,
          });
        }
      },
    });
  };

  handleCancel = e => {
    this.setState(
      {
        visible: false,
      },
      this.getBillServiceConfirmSignStatus
    );
  };

  // 保留两位小数
  toDecimal2 = x => {
    let f = parseFloat(x);
    if (isNaN(f)) {
      return false;
    }
    f = Math.round(x * 100) / 100;
    let s = f.toString();
    let rs = s.indexOf('.');
    if (rs < 0) {
      rs = s.length;
      s += '.';
    }
    while (s.length <= rs + 2) {
      s += '0';
    }
    return s;
  };

  isNoHomePage = () => {
    return (
      <div />
      // <Exception
      //   type="403"
      //   desc={formatMessage({id: 'app.exception.description.403'})}
      //   actions={<div/>}
      // />
    );
  };

  // 计费服务确认书点击按钮
  handleBillServiceButton = result => {
    const { dispatch } = this.props;
    dispatch({
      type: 'homePage/changeBillService',
      payload: result,
      callback: () => {
        if (result) {
          this.getAuthStatus({ contractType: 2 });
        }
      },
    });
  };

  getAuthStatus = param => {
    const { dispatch } = this.props;
    dispatch({
      type: 'Auth/getAuthStatus',
      payload: param,
      callback: result => {
        if (result.success) {
          router.push(result.data);
        } else {
          message.error(result.message);
        }
      },
    });
  };

  // 点击冻结
  handleMerchantStatus = () => {
    const { dispatch } = this.props;
    dispatch({
      type: 'homePage/selectFreezeReasons',
      callback: response => {
        if (response.success) {
          this.setState({
            merchantStatusVisible: true,
          });
        } else {
          message.error(response.message);
        }
      },
    });
  };

  // 暂不处理密码修改
  ignorePwd = () => {
    const { dispatch } = this.props;
    dispatch({
      type: 'homePage/ignorePwd',
      callback: response => {
        localStorage.setItem('isHomeNoticeModal', 'false');
        localStorage.setItem('clickError', 'false');
        localStorage.setItem('clickIdCard', 'false');
        this.setState(
          {
            allMessageVisible: false,
          },
          this.getBillServiceConfirmSignStatus
        );
      },
    });
  };

  render() {
    var settings = {
      dots: true,
      infinite: false,
      speed: 500,
      slidesToShow: 4,
      slidesToScroll: 4,
      initialSlide: 0,
      responsive: [
        {
          breakpoint: 1200,
          settings: {
            slidesToShow: 3,
            slidesToScroll: 3,
            infinite: true,
            dots: true,
            centerMode: false,
          },
        },
        {
          breakpoint: 900,
          settings: {
            slidesToShow: 2,
            slidesToScroll: 2,
            initialSlide: 2,
            infinite: true,
            dots: false,
            centerMode: false,
          },
        },
        {
          breakpoint: 480,
          settings: {
            slidesToShow: 1,
            slidesToScroll: 1,
            infinite: true,
            dots: false,
            centerMode: false,
          },
        },
      ],
    };
    const {
      loading,
      shipperLoading,
      authorizedLoading,
      getBillLoading,
      notificationsLoading,
      queryNewsLoading,
      homePage: { billServiceVisible },
    } = this.props;
    const {
      merchantBillInfo,
      shipperArray,
      notification,
      contentIdList,
      news,
      quotationData,
      visible,
      currentAuthorityValue,
      merchantInfo,
      isShowFrozenBalance,
      detalsmyBill,
      allMessageVisible,
      allMessageArray,
      merchantStatusVisible,
    } = this.state;

    const merchantStatus = ejfStatus => {
      let str = '不活动'; // 0
      if (ejfStatus === 1) {
        str = '活动';
      } else if (ejfStatus === 2) {
        str = '冻结';
      } else if (ejfStatus === 3) {
        str = '其他';
      }
      return str;
    };

    const shipperStatus = ejfStatus => {
      let str = '不活动'; // 0
      if (ejfStatus === '1') {
        str = '活动';
      } else if (ejfStatus === '2') {
        str = '冻结';
      } else if (ejfStatus === '3') {
        str = '其他';
      }
      return str;
    };

    const freezeReason = () => {
      const { freeze } = merchantInfo;
      let str = '';
      if (freeze.amountfreeze === '1') {
        str = str.length === 0 ? `${str}金额冻结` : `${str}、金额冻结`;
      }
      if (freeze.deliveryfreeze === '1') {
        str = str.length === 0 ? `${str}长期未发货冻结` : `${str}、长期未发货冻结`;
      }
      if (freeze.datafreeze === '1') {
        str = str.length === 0 ? `${str}资料不全冻结` : `${str}、资料不全冻结`;
      }
      return str;
    };

    const colorStr = (dataArray, item) => {
      const index = dataArray.indexOf(item);
      const str = index % 2 === 0 ? '#f6f8f9' : '#fff';
      return str;
    };

    const handleActionsChange = value => {
      if (value?.accountType === 1) {
        // 返回FBA
        return [
          <a
            key="option1"
            onClick={() => {
              router.push('/fba/fbaOrder');
            }}
            style={{
              height: '43px',
              lineHeight: '43px',
              color: '#52c41a',
              fontSize: '15px',
            }}
          >
            FBA下单
          </a>,
        ];
      } else if (value?.accountType === 2) {
        // 返回海外仓
        return [
          <a
            key="option1"
            onClick={() => {
              this.enterOverwareAction(value);
            }}
            style={{
              // display: isAuth('home:ejf:download') ? '' : 'none',
              height: '43px',
              lineHeight: '43px',
              color: '#52c41a',
              fontSize: '15px',
            }}
          >
            进入{value.pickWarehouseName}系统
          </a>,
        ];
      } else if (value?.accountType === 3) {
        // 返回中国仓
        return [
          <a
            key="option1"
            onClick={() => {
              this.enterOverwareAction(value);
            }}
            style={{
              // display: isAuth('home:ejf:download') ? '' : 'none',
              height: '43px',
              lineHeight: '43px',
              color: '#52c41a',
              fontSize: '15px',
            }}
          >
            进入中国仓系统
          </a>,
        ];
      } else {
        return [
          <a
            key="option1"
            onClick={() => {
              this.enterEjfACtion(value);
            }}
            style={{
              display: isAuth('abnormal:order') ? '' : 'none',
              height: '43px',
              lineHeight: '43px',
              color: '#52c41a',
              fontSize: '15px',
            }}
          >
            {value.customerCode == '102759' ||
            value.customerCode == '********' ||
            value.customerCode == '12024941'
              ? '进入制单系统EJF'
              : '创建订单'}
          </a>,
          // <a
          //   key="option2"
          //   onClick={() => {
          //     localStorage.setItem('currentShiperIndex', index.toString());
          //     router.push(`/merchant/shipperInfoList`);
          //   }}
          // >
          //   密钥
          // </a>,
          <Tooltip placement="bottom" title={`线下报价单已更新至${value.uploadTime}版`}>
            <a
              style={{
                display: isAuth('home:quotation:download') ? '' : 'none',
              }}
              key="option3"
              onClick={() => {
                this.showModal(value);
              }}
            >
              报价单下载
              {/*<Icon type="info-circle" style={{marginLeft:10, color:'red'}}></Icon>*/}
              <div style={{ color: 'red', fontSize: 12 }}>{value.uploadTime}版</div>
            </a>
          </Tooltip>,
        ];
      }
    };

    // 返回不同的仓库名
    const returnPickName = shop => {
      return shop?.accountType === 2
        ? `海外仓`
        : shop?.accountType === 3
        ? '中国仓'
        : shop?.pickWarehouseName;
    };

    const billValue = param => {
      let str = '';
      if (merchantBillInfo === null) {
        return str;
      }
      let value = merchantBillInfo.changedBalance;
      if (param === 'unSettledBalance') {
        value = merchantBillInfo.unSettledBalance;
      }
      if (param === 'SettledBalance') {
        value = merchantBillInfo.SettledBalance;
      }
      if (value === undefined || value === null) {
        return str;
      }

      if (param === 'frozenBalance') {
        value = merchantBillInfo.frozenBalance;
      }
      if (value === undefined || value === null || value === '') {
        return str;
      }
      if (value === 0) {
        return str;
      }
      str = value;
      return str;
    };
    const noFrozenBalance = {
      sm: {
        span: 8,
      },
      xs: {
        span: 24,
      },
    };
    const haveFrozenBalance = {
      sm: {
        span: 6,
      },
      xs: {
        span: 24,
      },
    };
    const showFrozenBalance = isShowFrozenBalance == '预付款' ? haveFrozenBalance : noFrozenBalance;

    return (
      <div>
        {currentAuthorityValue === null ? (
          <Card loading={authorizedLoading} />
        ) : currentAuthorityValue === 'merchant' ? (
          <div>
            {isAuth('is:have:home') ? (
              <div>
                {/* 个人信息 == */}
                <Row gutter={{ md: 0, lg: 16 }}>
                  <Col span={24} style={{ backgroundColor: '#f0f2f5' }}>
                    <Row gutter={{ md: 0, lg: 16 }}>
                      <Col md={24} lg={7}>
                        <Card bordered={false} loading={loading}>
                          {merchantInfo !== null && merchantInfo.merchant ? (
                            <Row>
                              <Col span={6} style={{ textAlign: 'center', marginRight: 10 }}>
                                <div>
                                  <Avatar size={64} src={avatar} />
                                </div>
                                {merchantStatus(merchantInfo.merchant.ejfStatus) === '冻结' ? (
                                  <Button
                                    style={{
                                      width: 42,
                                      height: 18,
                                      color: '#79b837',
                                      fontSize: 12,
                                      borderRadius: 100,
                                      borderColor: '#79b837',
                                      borderWidth: 1,
                                      padding: 0,
                                      marginTop: 5,
                                    }}
                                    onClick={this.handleMerchantStatus}
                                  >
                                    {merchantStatus(merchantInfo.merchant.ejfStatus)}
                                  </Button>
                                ) : (
                                  <Button
                                    style={{
                                      width: 42,
                                      height: 18,
                                      color: '#79b837',
                                      fontSize: 12,
                                      borderRadius: 100,
                                      borderColor: '#79b837',
                                      borderWidth: 1,
                                      padding: 0,
                                      marginTop: 5,
                                    }}
                                  >
                                    {merchantStatus(merchantInfo.merchant.ejfStatus)}
                                  </Button>
                                )}
                              </Col>
                              <Col span={16}>
                                <div
                                  onClick={() => {
                                    router.push(`/merchant/merchantInfoList`);
                                  }}
                                >
                                  {/*<Paragraph ellipsis={{ rows: 2}}>*/}
                                  {/*{merchantInfo.merchant.customerNameEn}你好吧你好吧年号吧你你你哦你哦你好吧你好吧年号吧你你你哦你哦*/}
                                  {/*</Paragraph>*/}
                                  <Button
                                    style={{
                                      fontSize: 18,
                                      textAlign: 'left',
                                      border: 'none',
                                      boxShadow: 'none',
                                      padding: '0px',
                                      color: '#52c41a',
                                    }}
                                  >
                                    {merchantInfo.merchant.customerNameEn}
                                  </Button>
                                </div>
                                <div>
                                  <span style={{ color: '#888888' }}>商户号</span>
                                  &nbsp;&nbsp;
                                  {merchantInfo.merchant.code}
                                </div>
                                <div>
                                  <span style={{ color: '#888888' }}>开户日期</span>
                                  &nbsp;&nbsp;
                                  {merchantInfo.merchant.createTime}
                                </div>
                                <div>
                                  <span style={{ color: '#888888' }}>支付周期</span>
                                  &nbsp;&nbsp;
                                  {merchantInfo.merchant.payCycleName}
                                </div>
                              </Col>
                            </Row>
                          ) : (
                            <div style={{ minHeight: 95, paddingTop: 10, textAlign: 'center' }}>
                              用户信息服务暂不可用
                            </div>
                          )}
                        </Card>
                      </Col>
                      {/*<Col />*/}
                      <Col md={24} lg={17}>
                        <Card bordered={false} style={{ minHeight: 143 }} loading={getBillLoading}>
                          {merchantBillInfo === undefined ? (
                            <div
                              style={{
                                width: '100%',
                                textAlign: 'center',
                              }}
                            >
                              <img src={noBill} style={{ height: '60%' }} />
                              <div style={{ fontSize: '18px', marginTop: '5px' }}>
                                账单服务暂时不可用
                              </div>
                            </div>
                          ) : (
                            <div>
                              <Row type="flex" justify="space-around" align="middle">
                                <Col {...showFrozenBalance}>
                                  {this.info(
                                    account_icon1,
                                    '#e4f1d7',
                                    '#79b837',
                                    '账户余额',
                                    billValue('changedBalance'),
                                    true,
                                    false,
                                    undefined,
                                    undefined,
                                    '/myBill/billList'
                                  )}
                                </Col>
                                <Col {...showFrozenBalance}>
                                  {this.info(
                                    account_icon2,
                                    '#fff5d5',
                                    '#ffcc2e',
                                    '未出账单金额',
                                    billValue('unSettledBalance'),
                                    true,
                                    false,
                                    undefined,
                                    undefined,
                                    '/myBill/bill/notOutBill'
                                  )}
                                </Col>
                                {isShowFrozenBalance == '预付款' ? (
                                  <Col {...showFrozenBalance} style={{ whiteSpace: 'nowrap' }}>
                                    {this.info(
                                      account_icon4,
                                      '#ffcac6',
                                      '#ff5043',
                                      '制单预扣金额',
                                      billValue('frozenBalance'),
                                      true,
                                      false,
                                      undefined,
                                      undefined,
                                      '/myBill/bill/withholdingBillList'
                                    )}
                                  </Col>
                                ) : null}
                                <Col {...showFrozenBalance} style={{ whiteSpace: 'nowrap' }}>
                                  {this.info(
                                    account_icon3,
                                    '#dbecff',
                                    '#4da1ff',
                                    '可用余额',
                                    billValue('SettledBalance'),
                                    false,
                                    detalsmyBill != 0,
                                    merchantInfo !== null && merchantInfo.merchant
                                      ? merchantInfo.merchant.payCycleName
                                      : '',
                                    undefined,
                                    undefined
                                  )}
                                </Col>
                              </Row>
                            </div>
                          )}
                        </Card>
                      </Col>
                    </Row>
                  </Col>
                </Row>
                {/* 制单账号 */}
                <Card className={styles.offlineCard} bordered={false} loading={shipperLoading}>
                  <h3 style={{ color: '#333', marginLeft: 25, marginBottom: 15 }}>
                    <Button
                      style={{
                        fontSize: 18,
                        textAlign: 'left',
                        border: 'none',
                        boxShadow: 'none',
                        padding: '0px',
                      }}
                      onClick={() => {
                        router.push(`/merchant/shipperInfoList`);
                      }}
                    >
                      制单账号
                      <span style={{ color: '#ACB5C3' }}>({shipperArray.length})</span>
                    </Button>
                  </h3>
                  <div style={{ margin: shipperArray.length > 4 ? '15px 35px' : '0px 20px' }}>
                    <Slider {...settings}>
                      {shipperArray &&
                        shipperArray.map((value, index) => (
                          <div style={{ marginLeft: 10, marginRigth: 10 }} key={value.id}>
                            <Card
                              className={styles.shipper}
                              hoverable
                              actions={handleActionsChange(value)}
                              style={{ borderRadius: 3, margin: 10 }}
                            >
                              <div
                                onClick={() => {
                                  localStorage.setItem('currentShiperIndex', index.toString());
                                  router.push(`/merchant/shipperInfoList`);
                                }}
                              >
                                <Row>
                                  <Col span={6}>
                                    <img src={shippericon} style={{ marginRight: 20 }} />
                                  </Col>
                                  <Col span={18} style={{ textAlign: 'left' }}>
                                    <div style={{ color: '#333', fontSize: 18 }}>
                                      {value.customerCode}
                                      <Button
                                        type="link"
                                        style={{
                                          float: 'right',
                                          marginTop: -8,
                                          paddingRight: 0,
                                        }}
                                      >
                                        {shipperStatus(value.ejfStatus != null && value.ejfStatus)}
                                      </Button>
                                    </div>
                                    <div style={{ color: '#888', fontSize: 14 }}>
                                      创建时间：{value.createTime.split(' ')[0]}
                                    </div>
                                    <div
                                      style={{
                                        color: '#333',
                                        fontSize: 20,
                                        fontWeight: 600,
                                      }}
                                    >
                                      {returnPickName(value)}
                                    </div>
                                  </Col>
                                </Row>
                              </div>
                            </Card>
                          </div>
                        ))}
                    </Slider>
                  </div>
                </Card>
                <Modal
                  title="选择报价单"
                  visible={visible}
                  onCancel={this.handleCancel}
                  destroyOnClose
                  footer=""
                >
                  <List
                    bordered={false}
                    dataSource={quotationData}
                    renderItem={item => (
                      <List.Item>
                        <a
                          style={{ textAlign: 'center' }}
                          onClick={() => this.addUserActionLog(32, 1)}
                          href={item.downLoadUrl}
                        >
                          {item.name}
                        </a>
                      </List.Item>
                    )}
                  />
                </Modal>
                {/*所有弹窗合并*/}
                <Modal
                  width={550}
                  visible={allMessageVisible}
                  footer={null}
                  maskClosable={false}
                  onCancel={() => {
                    localStorage.setItem('isHomeNoticeModal', 'false');
                    localStorage.setItem('clickError', 'false');
                    localStorage.setItem('clickIdCard', 'false');
                    this.setState(
                      {
                        allMessageVisible: false,
                      },
                      this.getBillServiceConfirmSignStatus
                    );
                  }}
                >
                  <div style={{ padding: '10px', textAlign: 'center' }}>
                    <h3>燕文重要提示</h3>
                    <ul style={{ listStyle: 'decimal' }}>
                      {allMessageArray.map((item, index) => {
                        return (
                          <li key={index}>
                            {/* {item.code}、  */}
                            <div className={styles.allMessageContent}>
                              <span>
                                <span style={{ fontWeight: 'bold' }}>【{item.titleHeader}】</span>
                                &nbsp;&nbsp;&nbsp;{item.text}
                              </span>
                              <div className={styles.allMessageBody}>
                                <span
                                  // style={{
                                  //   whiteSpace: 'nowrap',
                                  //   textOverflow: 'ellipsis',
                                  //   overflow: 'hidden',
                                  //   width: '72%',
                                  // }}
                                  style={{
                                    webkitBoxOrient: 'vertical',
                                  }}
                                  className={styles.allMessageDesc}
                                >
                                  {item.description}
                                </span>
                                <Space>
                                  <Popconfirm
                                    title="发起后不可撤销,请谨慎操作"
                                    visible={item.showUpdateModal}
                                    onConfirm={() => {
                                      this.handleOk(item);
                                    }}
                                    //  okButtonProps={{ loading: confirmLoading }}
                                    onCancel={() => this.handleMessageCancel(item)}
                                    getPopupContainer={triggerNode => triggerNode.parentNode} ///增加这个定位去解决滑跑
                                  >
                                    <Button type="link" onClick={() => this.clickItem(item)}>
                                      {item.buttonTitle}
                                    </Button>
                                  </Popconfirm>
                                  {item.titleHeader == '密码修改提醒' ? (
                                    <Button type="link" onClick={() => this.ignorePwd()}>
                                      暂不处理
                                    </Button>
                                  ) : null}
                                </Space>
                              </div>
                            </div>
                          </li>
                        );
                      })}
                    </ul>
                  </div>
                </Modal>
                {/* 公告和咨询 */}
                <Row gutter={16} className={styles.message}>
                  <Col span={12}>
                    <Card
                      title={
                        this.state.noticeCount == 0 ? '公告' : `公告(${this.state.noticeCount})`
                      }
                      bordered={false}
                      loading={notificationsLoading}
                      extra={
                        <a
                          href="#"
                          onClick={() => {
                            localStorage.setItem('isHome', 'true');
                            router.push(`/homePage/noticeList/${0}`);
                          }}
                        >
                          更多
                        </a>
                      }
                    >
                      <List
                        // header={<div>公告</div>}
                        bordered={false}
                        split={false}
                        dataSource={notification}
                        renderItem={item => (
                          <List.Item
                            style={{
                              backgroundColor: colorStr(notification, item),
                              display: 'block',
                            }}
                            onClick={() => {
                              let param = {
                                order: 'asc',
                                limit: 10,
                                currentPage: 1,
                                context: '',
                                categoryId: 0,
                                top: null,
                              };
                              router.push({
                                pathname: '/homePage/notificationMessage',
                                state: {
                                  contentId: item.contentId,
                                  conList: contentIdList,
                                  param: param,
                                },
                              });
                            }}
                          >
                            <div className={styles.textOver}>
                              <span
                                style={{
                                  fontSize: 20,
                                  fontWeight: 800,
                                  paddingLeft: 15,
                                }}
                              >
                                ·
                                <span
                                  style={{
                                    display: item.saw == 0 ? '' : 'none',
                                    background: 'red',
                                    color: '#fff',
                                    fontSize: '12px',
                                    fontWeight: '400',
                                    padding: '0 2px',
                                    marginLeft: '8px',
                                    borderRadius: '1px',
                                    position: 'relative',
                                    top: '-7px',
                                    fontFamily: 'monospace',
                                  }}
                                >
                                  NEW
                                </span>
                                &nbsp;&nbsp;
                              </span>
                              {item.title}...
                            </div>
                            <span
                              style={{
                                float: 'right',
                                paddingRight: 15,
                                width: '90px',
                                lineHeight: '40px',
                              }}
                            >
                              {item.inputdate}
                            </span>
                          </List.Item>
                        )}
                      />
                    </Card>
                  </Col>
                  <Col span={12}>
                    <Card
                      title="资讯"
                      bordered={false}
                      loading={queryNewsLoading}
                      extra={
                        <a
                          href="#"
                          onClick={() => {
                            router.push(`/homePage/notice/${1}`);
                          }}
                        >
                          更多
                        </a>
                      }
                    >
                      <List
                        bordered={false}
                        split={false}
                        dataSource={news}
                        renderItem={item => (
                          <List.Item
                            style={{
                              backgroundColor: colorStr(news, item),
                              display: 'block',
                            }}
                            onClick={() => {
                              router.push({
                                pathname: '/homePage/notificationMessage',
                                state: {
                                  contentId: item.contentId,
                                  conList: [],
                                  param: { categoryId: 1 },
                                },
                              });
                            }}
                          >
                            <div className={styles.textOver}>
                              <span
                                style={{
                                  fontSize: 20,
                                  fontWeight: 800,
                                  paddingLeft: 15,
                                }}
                              >
                                ·&nbsp;&nbsp;
                              </span>
                              {item.title}...
                            </div>
                            <span
                              style={{
                                float: 'right',
                                paddingRight: 15,
                                width: '90px',
                                lineHeight: '40px',
                              }}
                            >
                              {item.inputdate}
                            </span>
                          </List.Item>
                        )}
                      />
                    </Card>
                  </Col>
                </Row>
                {/*  计费服务确认书弹窗*/}
                <BillingServiceModal
                  billServiceVisible={billServiceVisible}
                  handleChange={this.handleBillServiceButton}
                />
                {/* 商户冻结弹窗 */}
                <MerchantStatusModal
                  {...this.props}
                  visible={merchantStatusVisible}
                  onCancel={() => {
                    this.setState(
                      {
                        merchantStatusVisible: false,
                      },
                      () => {
                        this.props.dispatch({
                          type: 'homePage/getHomePageMerchantInfo',
                          callback: response => {
                            if (response.success) {
                              this.setState({
                                isShowFrozenBalance: response.data.merchant.payCycleName,
                                merchantInfo: response.data,
                              });
                            } else {
                              this.setState({
                                merchantInfo: null,
                              });
                            }
                          },
                        });
                      }
                    );
                  }}
                />
              </div>
            ) : (
              this.isNoHomePage()
            )}
          </div>
        ) : (
          <Redirect to={currentAuthorityValue} />
        )}
      </div>
    );
  }
}

export default HomePage;
