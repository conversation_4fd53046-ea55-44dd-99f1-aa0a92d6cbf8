import React, { useState, useImperativeHandle, useRef } from 'react';
import { Mo<PERSON>, Divider, Button } from 'antd';
import { openSignModal } from '@/utils/utils';

export default function BillingServiceModal(props) {
  const { dispatch, onFinish, modalRef } = props;
  const [billServiceVisible, setBillServiceVisible] = useState(false);

  useImperativeHandle(modalRef, () => ({
    show: () => {
      setBillServiceVisible(true);
    },
  }));

  const handleClick = result => {
    getSignBillServiceBook(result);
  };

  const getSignBillServiceBook = result => {
    dispatch({
      type: 'homePage/getSignBillServiceBook',
      payload: {
        viewContract: !result,
      },
      callback: response => {
        if (response.success) {
          // onFinish();
          if (result) {
            window.location.href = response.data?.signUrl;
          } else {
            window.open(response.data?.signUrl, '_blank');
          }
        }
      },
    });
  };

  return (
    <Modal
      width="60%"
      closable={false}
      open={billServiceVisible}
      footer={null}
      bodyStyle={{ padding: '10px' }}
      style={{ marginTop: '10px' }}
    >
      <div>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div />
          <h2 style={{ textAlign: 'center' }}>温馨提示</h2>
          <h2
            onClick={() => setBillServiceVisible(false)}
            style={{ fontWeight: 'bold', color: '#bdbdbd', cursor: 'pointer' }}
          >
            X
          </h2>
        </div>
        <Divider style={{ margin: '5px' }} />
        <div style={{ padding: '10px 30px', fontSize: '17px' }}>
          <p>尊敬的用户：</p>
          <p style={{ textIndent: '30px' }}>感谢您对燕文物流一直以来的信任与支持！</p>
          <p style={{ textIndent: '30px' }}>
            为了向您提供更好的服务，保障您的货物到仓后快速处理发出，我们更新了服务流程。请您仔细阅读本《服务确认书》的内容，并确定了解我们对您委托燕文物流发运的所有泡重计费产品，均不再进行异常件通知，即：
          </p>
          <p style={{ textIndent: '30px', fontWeight: 'bold' }}>
            货物到仓后将直接按照您所选择的泡货确认方式进行处理，我们不再就泡重计费等事宜对您另行通知。
          </p>
          <p style={{ textIndent: '30px' }}>
            为提醒您特别注意，我们已对部分条款予以加粗对您进行特别提示。如您在阅读过程中对本《服务确认书》内容提示信息有任何疑问，请勿进行下一步操作，并通过客服与我们联系。
          </p>
          <p style={{ textIndent: '30px' }}>
            在您勾选或点击确认、同意或输入短信验证码时，即表示您已充分阅读、理解并接受本《服务确认书》的全部内容，并对可能产生的法律后果予以了充分确认。
          </p>
        </div>
        <Divider style={{ margin: '5px' }} />
        <div
          style={{
            padding: '15px 0 15px 0',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          <Button
            onClick={() => handleClick(false)}
            style={{ width: '15%', marginRight: '5%' }}
            size="large"
          >
            预览下载
          </Button>
          <Button
            onClick={() => handleClick(true)}
            style={{ width: '15%', marginLeft: '5%' }}
            type="primary"
            size="large"
          >
            立即签署
          </Button>
        </div>
      </div>
    </Modal>
  );
}
