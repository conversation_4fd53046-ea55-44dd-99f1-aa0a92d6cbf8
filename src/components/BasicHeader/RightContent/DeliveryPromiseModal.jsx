import React, { useState, useImperativeHandle, useRef } from 'react';
import { Mo<PERSON>, Divider, Button } from 'antd';
import { openSignModal } from '@/utils/utils';
/**
 * 发货承诺书
 * @param {*} props
 * @returns
 */
export default function DeliveryPromiseModal(props) {
  const { dispatch, onFinish, modalRef } = props;
  const signModalRef = useRef();
  const [billServiceVisible, setBillServiceVisible] = useState(false);

  useImperativeHandle(modalRef, () => ({
    show: () => {
      setBillServiceVisible(true);
    },
  }));

  const handleClick = result => {
    getSignBillServiceBook(result);
  };

  const getSignBillServiceBook = result => {
    dispatch({
      type: 'homePage/getDeliveryPromiseBook',
      payload: {
        viewContract: !result,
      },
      callback: response => {
        if (response.success) {
          // onFinish();
          if (result) {
            window.location.href = response.data?.signUrl;
          } else {
            window.open(response.data?.signUrl, '_blank');
          }
        }
      },
    });
  };

  return (
    <Modal
      width="60%"
      closable={false}
      open={billServiceVisible}
      footer={null}
      bodyStyle={{ padding: '10px' }}
      style={{ marginTop: '10px' }}
    >
      <div>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div />
          <h2 style={{ textAlign: 'center' }}>温馨提示</h2>
          <h2
            onClick={() => setBillServiceVisible(false)}
            style={{ fontWeight: 'bold', color: '#bdbdbd', cursor: 'pointer' }}
          >
            X
          </h2>
        </div>
        <Divider style={{ margin: '5px' }} />
        <div style={{ padding: '10px 30px', fontSize: '17px' }}>
          <p>尊敬的用户：</p>
          <p style={{ textIndent: '30px' }}>感谢您对燕文物流一直以来的信任与支持！</p>
          <p style={{ textIndent: '30px' }}>
            为了向您提供更好的服务，也为了您与燕文可以更好的继续合作，保障您的货物到仓后快速得到处理，请您仔细阅读《发货承诺书》相关内容，并确定了解燕文物流对您所发货物的安全性、合规性等要求，
            <span style={{ fontWeight: 'bold' }}>
              如在后续合作过程中，您依旧出现通过燕文物流寄递危险品、违禁品、侵犯知识产权物品等情形的，燕文物流将无责解除与您之间的服务合同，并有权要求您承担违约责任，情节严重的，将提交相应机关处理。
            </span>
          </p>
          <p style={{ textIndent: '30px' }}>
            为提醒您特别注意，我们已对《发货承诺书》部分描述予以加粗并对您进行提示，如您在阅读过程中对《发货承诺书》内容提示信息有任何疑问，请勿进行下一步操作，并通过客服与我们取得联系。
          </p>
          <p>
            在您勾选或点击确认、同意或输入短信验证码或在个人终端签字确认时，即表示您已充分阅读、理解并接受《发货承诺书》的全部内容，并对可能产生的法律后果予以充分确认。
          </p>
        </div>
        <Divider style={{ margin: '5px' }} />
        <div
          style={{
            padding: '15px 0 15px 0',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          <Button
            onClick={() => handleClick(false)}
            style={{ width: '15%', marginRight: '5%' }}
            size="large"
          >
            预览下载
          </Button>
          <Button
            onClick={() => handleClick(true)}
            style={{ width: '15%', marginLeft: '5%' }}
            type="primary"
            size="large"
          >
            立即签署
          </Button>
        </div>
      </div>
    </Modal>
  );
}
