import React, { useEffect, useState } from 'react';
import { WechatOutlined, CustomerServiceOutlined } from '@ant-design/icons';
import { Popover, Descriptions, Divider, Modal } from 'antd';
// @ts-ignore
import wechatImg from '../../assets/weiLogo.png';
import QRCodeModal from '../QRCodeModal';
import PageLoading from '../PageLoading';

const Index = ({ dispatch, location: { pathname } }) => {
  const [previewOpen, setPreviewOpen] = useState(false);
  const [previewImage, setPreviewImage] = useState();
  const [accountInfo, setAccountInfo] = useState<any>();
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (open) {
      if (pathname.includes('fba')) {
        // 请求fba
        getAccountInfo();
      } else {
        // 请求小包
        getPacketBaseInfo();
      }
    }
  }, [pathname, open]);

  const getAccountInfo = () => {
    dispatch({
      type: 'fba/fbaPageInfo',
      callback: response => {
        setLoading(false);
        if (response.success) {
          setAccountInfo({
            ...accountInfo,
            ...response.data,
          });
        } else {
          setAccountInfo({});
        }
      },
    });
  };

  const getPacketBaseInfo = () => {
    dispatch({
      type: 'smallBag/getPacketBaseInfo',
      callback: response => {
        setLoading(false);
        if (response.success) {
          const saleman = response?.data?.salesman;
          const supportStaff = response?.data?.supportStaff;
          setAccountInfo({
            ...accountInfo,
            saleName: saleman?.username,
            salePhone: saleman?.phone,
            enterpriseWeChat: saleman?.enterpriseWeChat,
            serviceName: supportStaff?.username,
            servicePhone: supportStaff?.phone,
            receiverEnterpriseWeChat: supportStaff?.enterpriseWeChat,
          });
          //  setPacketBaseInfo(response.data ?? packetBaseInfo);
        } else {
          setAccountInfo({});
        }
      },
    });
  };

  const content = () => {
    return loading ? (
      <PageLoading paddingTop={0} />
    ) : (
      <>
        <Descriptions layout="vertical">
          <Descriptions.Item label="业务经理">
            {accountInfo?.saleName &&
            accountInfo?.enterpriseWeChat &&
            accountInfo?.enterpriseWeChat !== '' ? (
              <a
                onClick={() => {
                  setPreviewOpen(true);
                  setPreviewImage(accountInfo?.enterpriseWeChat);
                }}
              >
                <img style={{ width: '18px', height: '16px' }} src={wechatImg} alt="" />
                &nbsp;&nbsp;
                <span>{accountInfo?.saleName ?? ''}</span>
                &nbsp;&nbsp;
                <span>{accountInfo?.salePhone ?? ''}</span>
              </a>
            ) : (
              <span style={{ color: '#79b837' }}>
                {accountInfo?.saleName?.concat(' ')?.concat(accountInfo?.salePhone ?? '') ?? ''}
              </span>
            )}
          </Descriptions.Item>
        </Descriptions>
        <Divider type="vertical" />
        {pathname.includes('smallBag') &&
        accountInfo?.serviceName === accountInfo?.saleName ? null : (
          <Descriptions layout="vertical">
            <Descriptions.Item label="客服助手">
              {accountInfo?.serviceName &&
              accountInfo?.receiverEnterpriseWeChat &&
              accountInfo?.receiverEnterpriseWeChat !== '' ? (
                <a
                  onClick={() => {
                    setPreviewOpen(true);
                    setPreviewImage(accountInfo?.receiverEnterpriseWeChat);
                  }}
                >
                  <img style={{ width: '18px', height: '16px' }} src={wechatImg} alt="" />
                  &nbsp;&nbsp;
                  <span>{accountInfo?.serviceName ?? ''}</span>
                  &nbsp;&nbsp;
                  <span>
                    {/* {shipperDetail.referenceEmployee.mobile === null
                  ? shipperDetail.referenceEmployee.phone
                  : shipperDetail.referenceEmployee.mobile} */}
                    {accountInfo?.servicePhone ?? ''}
                  </span>
                </a>
              ) : (
                <span style={{ color: '#79b837' }}>
                  {accountInfo?.serviceName?.concat(' ')?.concat(accountInfo?.servicePhone ?? '') ??
                    ''}
                </span>
              )}
            </Descriptions.Item>
          </Descriptions>
        )}
      </>
    );
  };

  return (
    <>
      <div
        onClick={() => {
          if (!open) {
            setLoading(true);
            setOpen(true);
          }
        }}
        style={{
          cursor: 'pointer',
          position: 'fixed',
          zIndex: 999,
          right: '10px',
          bottom: '30px',
          fontSize: '14px',
        }}
      >
        <div
          style={{
            backgroundColor: 'rgb(82, 196, 26)',
            pointerEvents: 'none',
            cursor: 'pointer',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            position: 'relative',
            userSelect: 'none',
            borderRadius: '50%',
            boxShadow: 'rgba(82, 196, 26, 0.2) 0px 4px 8px 0px',
            padding: '16px',
          }}
        >
          <CustomerServiceOutlined style={{ color: 'white', fontSize: '45px' }} />
        </div>
      </div>
      {open && (
        <Modal open={open} footer={null} onCancel={() => setOpen(false)} destroyOnClose>
          {content()}
        </Modal>
      )}
      {previewOpen && (
        <QRCodeModal open={previewOpen} url={previewImage} onCancel={() => setPreviewOpen(false)} />
      )}
    </>
  );
};

export default Index;
