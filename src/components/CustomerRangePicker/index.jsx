import React, { useState } from 'react';
import { DatePicker, Space, Button, TimePicker } from 'antd';
import styles from './index.less';
import { RightOutlined } from '@ant-design/icons';
import moment from 'moment';
import { useUpdateEffect } from 'ahooks';

const { RangePicker } = DatePicker;

const sharedProps = {
  onMouseDown(e) {
    e.stopPropagation();
    e.preventDefault();
  },
};
function DateInput({ dateTime }) {
  return (
    <div
      className="w-full h-full text-center"
      style={{ border: '1px solid #dcdfe6', borderRadius: '2px', padding: '4px 27px 4px 11px' }}
    >
      {dateTime || '请选择日期'}
    </div>
  );
}

const Index = props => {
  const { value, onChange, format } = props;
  const [open, setOpen] = useState(false);
  const [startTimePickerValue, setStartTimePickerValue] = useState(moment('00:00:00', 'HH:mm:ss'));
  const [endTimePickerValue, setEndTimePickerValue] = useState(moment('23:59:59', 'HH:mm:ss'));
  const [startTime, setStartTime] = useState(undefined);
  const [endTime, setEndTime] = useState(undefined);

  useUpdateEffect(() => {
    if (value) {
      const [startTime, endTime] = value;
      setStartTime(startTime ? moment(startTime, 'YYYY-MM-DD HH:mm:ss') : undefined);
      setEndTime(endTime ? moment(endTime, 'YYYY-MM-DD HH:mm:ss') : undefined);
    }
  }, [value]);

  useUpdateEffect(() => {
    if (value && startTimePickerValue && endTimePickerValue) {
      const startTime = `${value?.[0]?.format('YYYY-MM-DD')} ${startTimePickerValue.format(
        'HH:mm:ss'
      )}`;
      const endTime = `${value?.[1]?.format('YYYY-MM-DD')} ${endTimePickerValue.format(
        'HH:mm:ss'
      )}`;
      onChange(value, [startTime, endTime]);
    }
  }, [startTimePickerValue, endTimePickerValue]);

  function handleChange(date, dateString) {
    console.log('date', date);
    if (date) {
      const startTime = date[0] ? date[0] : undefined;
      const endTime = date[1] ? date[1] : undefined;
      setStartTime(startTime);
      setEndTime(endTime);
      setStartTimePickerValue(moment('00:00:00', 'HH:mm:ss'));
      setEndTimePickerValue(moment('23:59:59', 'HH:mm:ss'));
      onChange(date, dateString);
    } else {
      setOpen(false);
      setStartTime(undefined);
      setEndTime(undefined);
      onChange(date, dateString);
    }
  }

  function panelRender(node) {
    return (
      <div style={{ display: 'flex' }} {...sharedProps}>
        <Space direction="vertical" style={{ width: '100%', borderRight: '1px solid #e0e0e0' }}>
          <Button type="text" onClick={() => handleClickTimeType('today')}>
            今天
          </Button>
          <Button type="text" onClick={() => handleClickTimeType('last7Days')}>
            近7天
          </Button>
          <Button type="text" onClick={() => handleClickTimeType('lastMonth')}>
            近一月
          </Button>
          <Button type="text" onClick={() => handleClickTimeType('last3Months')}>
            近三月
          </Button>
        </Space>
        <div style={{ display: 'flex', flexDirection: 'column' }}>
          <Space
            style={{
              width: '100%',
              justifyContent: 'space-around',
              alignItems: 'center',
              padding: 5,
              borderBottom: '1px solid #e0e0e0',
            }}
          >
            <DateInput dateTime={value?.[0]?.format('YYYY-MM-DD')} />
            <TimePicker
              value={startTimePickerValue}
              onChange={(time, timeString) => {
                setStartTimePickerValue(time);
              }}
            />
            <RightOutlined />
            <DateInput dateTime={value?.[1]?.format('YYYY-MM-DD')} />
            <TimePicker
              value={endTimePickerValue}
              onChange={(time, timeString) => {
                setEndTimePickerValue(time);
              }}
            />
          </Space>
          <div>{node}</div>
          <div
            style={{
              borderTop: '1px solid #e0e0e0',
              padding: 5,
              display: 'flex',
              justifyContent: 'flex-end',
            }}
          >
            <Space>
              <Button onClick={() => setOpen(false)}>关闭</Button>
              <Button onClick={() => setOpen(false)}>确定</Button>
            </Space>
          </div>
        </div>
      </div>
    );
  }

  const handleClickTimeType = type => {
    let newStartTime;
    let newEndTime;
    setStartTimePickerValue(moment('00:00:00', 'HH:mm:ss'));
    setEndTimePickerValue(moment('23:59:59', 'HH:mm:ss'));
    if (type === 'today') {
      newStartTime = moment();
      newEndTime = moment();
    }
    if (type === 'last7Days') {
      newStartTime = moment().subtract(7, 'days');
      newEndTime = moment();
    }
    if (type === 'lastMonth') {
      newStartTime = moment().subtract(1, 'month');
      newEndTime = moment();
    }
    if (type === 'last3Months') {
      newStartTime = moment().subtract(3, 'months');
      newEndTime = moment();
    }
    setStartTime(newStartTime);
    setEndTime(newEndTime);
    const startTimeStr = `${newStartTime.format('YYYY-MM-DD')} 00:00:00`;
    const endTimeStr = `${newEndTime.format('YYYY-MM-DD')} 23:59:59`;
    onChange([newStartTime, newEndTime], [startTimeStr, endTimeStr]);
    setOpen(false);
  };
  // ${styles.myRangePicker}
  return (
    <RangePicker
      {...props}
      value={[startTime, endTime]}
      format={format}
      className={`w-full`}
      open={open}
      onChange={handleChange}
      onFocus={() => {
        setOpen(true);
      }}
      // onBlur={() => {
      //   setOpen(false);
      // }}
      panelRender={panelRender}
    />
  );
};

export default Index;
