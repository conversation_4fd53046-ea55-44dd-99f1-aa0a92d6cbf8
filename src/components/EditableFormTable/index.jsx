import { PlusOutlined } from '@ant-design/icons';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Input, message, Popconfirm, Select, Table, Upload } from 'antd';
import _ from 'lodash';
import React from 'react';
import './index.less';
import { computedNumber, keepTwoFloat } from '@/utils/utils';

const EditableContext = React.createContext();
const { Option } = Select;

const EditableRow = ({ form, index, ...props }) => {
  return (
    <EditableContext.Provider value={form}>
      <tr {...props} />
    </EditableContext.Provider>
  );
};

const EditableFormRow = Form.create()(EditableRow);

class EditableCell extends React.Component {
  state = {
    editing: false,
    brandTypeValue: undefined,
  };

  componentDidMount() {
    if (this.props?.onRef !== undefined) {
      this.props.onRef(this);
    }
    // console.log(`this.props`, this.props);
  }

  toggleEdit = () => {
    const editing = !this.state.editing;
    this.setState({ editing }, () => {
      if (editing) {
        if (this.props.inputType === 'text') {
          this.input.focus();
        }
      }
    });
  };

  resetForm = () => {
    this.form.resetFields();
  };

  save = e => {
    const { record, handleSave, sendError, bubbleCoefficient } = this.props;

    this.form.validateFieldsAndScroll((error, values) => {
      // console.log(values);
      if (e && error && error[e.currentTarget.id]) {
        sendError(error[e.currentTarget.id]);
        return;
      }
      sendError(undefined);
      // this.toggleEdit();
      let value;

      if (record.key.split('box').length > 1) {
        // 箱单信息
        value = { ...record };
        let vWeight;
        if (value.width === undefined || value.length === undefined || value.height === undefined) {
          vWeight = 0.0;
        } else {
          // vWeight = keepTwoFloat(
          //   (parseInt(value.width) * parseInt(value.length) * parseInt(value.height)) / 6000,
          //   2
          // );
          vWeight = keepTwoFloat(
            (parseInt(value.width) * parseInt(value.length) * parseInt(value.height)) /
              bubbleCoefficient,
            2
          );
        }
        value.predictVolumeWeight = vWeight;
      } else {
        let images = record['images'];
        value = {
          ...record,
          // ...values,
          images,
        };
      }
      handleSave(value);
    });
  };

  changeTXT = e => {
    const { record, handleSave, sendError, bubbleCoefficient } = this.props;
    switch (e.target.placeholder) {
      case '请输入FBA标签号/箱号':
        record.tagNumber = e.target.value == '' ? undefined : e.target.value;
        break;
      case '请输入Reference ID':
        record.amazonReferenceId = e.target.value == '' ? undefined : e.target.value;
        break;
      case '请输入单箱长（CM）':
        record.length = e.target.value == '' ? undefined : e.target.value;
        break;
      case '请输入单箱宽（CM）':
        record.width = e.target.value == '' ? undefined : e.target.value;
        break;
      case '请输入单箱高（CM）':
        record.height = e.target.value == '' ? undefined : e.target.value;
        break;
      case '请输入单箱重量（KG）':
        record.boxWeight = e.target.value == '' ? undefined : e.target.value;
        break;
      case '请输入中文品名':
        record.chineseName = e.target.value == '' ? undefined : e.target.value;
        break;
      case '请输入英文品名':
        record.englishName = e.target.value == '' ? undefined : e.target.value;
        break;
      case '请输入产品SKU':
        record.productSku = e.target.value == '' ? undefined : e.target.value;
        break;
      case '请输入申报单价（USD）':
        record.declareUnitPrice = e.target.value == '' ? undefined : e.target.value;
        break;
      case '请输入数量（个）':
        record.quantity = e.target.value == '' ? undefined : e.target.value;
        break;
      case '请输入海关编码':
        record.hsCode = e.target.value == '' ? undefined : e.target.value;
        break;
      case '请输入材质':
        record.texture = e.target.value == '' ? undefined : e.target.value;
        break;
      case '请输入型号':
        record.type = e.target.value == '' ? undefined : e.target.value;
        break;
      case '请输入用途':
        record.purpose = e.target.value == '' ? undefined : e.target.value;
        break;
      case '请输入品牌':
        record.brand = e.target.value == '' ? undefined : e.target.value;
        break;
      case '请输入产品图片链接':
        record.linkUrl = e.target.value == '' ? undefined : e.target.value;
        break;
      case '请输入产品销售链接':
        record.saleLinkUrl = e.target.value == '' ? undefined : e.target.value;
        break;
      case '请输入重量（KG）':
        record.weight = e.target.value == '' ? undefined : e.target.value;
        break;
      case '请输入净重 (KG)':
        record.netWeight = e.target.value == '' ? undefined : e.target.value;
        break;
      case '请输入采购单价 (CNY)':
        record.purchasePrice = e.target.value == '' ? undefined : e.target.value;
        break;
      case '请输入备注':
        record.remark = e.target.value == '' ? undefined : e.target.value;
        break;
      case '':
        record.brandType =
          this.state.brandTypeValue == undefined ? undefined : this.state.brandTypeValue;
    }
    if (e.target.placeholder.includes('申报单价')) {
      record.declareUnitPrice = e.target.value == '' ? undefined : e.target.value;
    }
    // console.log(record);
    this.save(e);
    // this.setState({
    //   editing: false
    // }, () => this.save(e))
  };

  beforeUpload = (file, fileList) => {
    const { record, beforeUpload } = this.props;
    beforeUpload(file, fileList, record);
    // this.toggleEdit();
  };

  handleRemove = file => {
    const { record, handleRemove } = this.props;
    handleRemove(file, record);
  };

  showModelImage = () => {
    const { record, showModelImage } = this.props;
    showModelImage(record.images[0].url);
  };

  // 修改品牌类型
  handleChangebarndType = value => {
    // this.setState({
    //   brandTypeValue: value,
    // });
    const { record } = this.props;
    record.brandType = value;
    this.save();
  };
  renderCell = form => {
    this.form = form;
    const {
      dataIndex,
      title,
      inputType,
      record,
      index,
      children,
      required,
      pattern,
      max,
    } = this.props;
    const { editing } = this.state;
    return inputType === 'text' ? (
      editing ? (
        <Form.Item style={{ margin: 0, width: '100%' }}>
          {form.getFieldDecorator(dataIndex, {
            rules: [
              {
                required: required,
                message:
                  dataIndex == 'length' || dataIndex == 'height' || dataIndex == 'width'
                    ? `请输入整数${title}`
                    : `请输入${title}`,
              },
              pattern !== undefined
                ? {
                    pattern: pattern,
                    message:
                      dataIndex == 'chineseName'
                        ? `请输入正确的${title}`
                        : dataIndex == 'englishName' ||
                          dataIndex == 'quantity' ||
                          dataIndex == 'hsCode'
                        ? `请检查${title}中是否包含横杠，空格，括号等特殊符号`
                        : dataIndex == 'texture' ||
                          dataIndex == 'type' ||
                          dataIndex == 'purpose' ||
                          dataIndex == 'brand'
                        ? `请检查${title}是否包含特殊符号`
                        : dataIndex == 'linkUrl' || dataIndex == 'saleLinkUrl'
                        ? `请使用格式正确的${title}`
                        : dataIndex == 'amazonReferenceId'
                        ? 'Referece ID必填，仅支持英文、数字'
                        : `请使用正确的${title}`,
                  }
                : {},
              max !== undefined
                ? {
                    max: max,
                    message: `${title}不能超过${max}字符`,
                  }
                : {},
            ],
            initialValue: record[dataIndex],
          })(
            <Input
              allowClear
              ref={node => (this.input = node)}
              onPressEnter={this.save}
              onBlur={this.save}
              onChange={this.changeTXT}
              placeholder={`请输入${title}`}
            />
          )}
        </Form.Item>
      ) : (
        <div
          className="editable-cell-value-wrap"
          style={{ paddingRight: 24 }}
          onClick={() => this.toggleEdit()}
        >
          {children}
        </div>
      )
    ) : inputType === 'select' ? (
      <Form.Item style={{ margin: 0, width: '100%' }}>
        {form.getFieldDecorator(dataIndex, {
          initialValue: record[dataIndex] == null ? '' : String(record[dataIndex]),
          rules: [
            {
              required: true,
              message: '请选择品牌类型',
            },
          ],
        })(
          <Select
            style={{
              width: 200,
            }}
            placeholder="请选择品牌类型"
            onChange={this.handleChangebarndType}
            // onBlur={e => this.changeTXT(e)}
          >
            <Option value="1">无品牌</Option>
            <Option value="2">境内自主品牌</Option>
            <Option value="3">境内收购品牌</Option>
            <Option value="4">境外品牌(贴牌生产)</Option>
            <Option value="5">境外品牌(其他)</Option>
          </Select>
        )}
      </Form.Item>
    ) : (
      <Form.Item style={{ margin: 0, width: '100%' }}>
        {form.getFieldDecorator(dataIndex, {
          valuePropName: 'fileList',
          rules: [
            {
              required: required,
              message: `请上传${title}!`,
            },
          ],
          initialValue: record[dataIndex],
        })(
          <div className="uploadImage">
            <Upload
              fileList={record[dataIndex]}
              listType="picture-card"
              beforeUpload={(file, fileList) => this.beforeUpload(file, fileList)}
              accept="image/jpeg,image/png,image/gif,.zip"
              onPreview={file => {
                if (
                  file.name.includes('pdf') ||
                  file.name.includes('doc') ||
                  file.name.includes('docx') ||
                  file.name.includes('xls') ||
                  file.name.includes('xlsx') ||
                  file.name.includes('zip')
                ) {
                  message.warning('该文件不支持预览！');
                } else {
                  this.showModelImage();
                }
              }}
              onRemove={this.handleRemove}
            >
              {record[dataIndex].length >= 1 ? null : (
                <div>
                  <PlusOutlined />
                  <div className="ant-upload-text">上传</div>
                </div>
              )}
            </Upload>
          </div>
        )}
      </Form.Item>
    );
  };

  render() {
    const {
      editable,
      dataIndex,
      title,
      record,
      index,
      handleSave,
      children,
      ...restProps
    } = this.props;
    return (
      <td {...restProps}>
        {editable ? (
          <EditableContext.Consumer>{this.renderCell}</EditableContext.Consumer>
        ) : (
          children
        )}
      </td>
    );
  }
}

let oldColumns = [];

class EditableTable extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      data: props.data,
      footer: props.footer,
      uploadLoading: false,
      editingKey: '',
      selectedRowKeys: [],
      selectedRows: [],
    };
  }

  componentDidMount() {
    if (this.props?.onRef !== undefined) {
      this.props.onRef(this);
    }
  }

  componentWillReceiveProps(props) {
    if (props.title == '申报信息' && props.data.length != this.state.data.length) {
      this.setState({
        data: [...props.data],
        selectedRowKeys: [],
        selectedRows: [],
      });
    }
  }
  beforeUpload = (file, fileList, record) => {
    this.setState({
      uploadLoading: true,
    });
    this.props.tableBeforeUpload(file, fileList, record);
  };

  handleRemove = (file, record) => {
    this.props.handleRemove(file, record);
  };

  showModelImage = url => {
    this.props.showModelImage(url);
  };

  changeLoadingState = state => {
    this.setState({
      uploadLoading: state,
    });
  };

  handleSave = row => {
    const newData = [...this.state.data];
    const index = newData.findIndex(item => row.key === item.key);
    if (index > -1) {
      const item = newData[index];
      newData.splice(index, 1, {
        ...item,
        ...row,
      });
      this.setState({ data: newData });
    } else {
      newData[0] = { ...row };
      this.setState({ data: newData });
    }
    // console.log(newData);
    this.props.onSaveTableBox(newData);
  };

  // 修改表格数据
  changeDataTable = value => {
    this.setState({
      data: value,
    });
  };

  handleDelete = record => {
    const { data } = this.state;
    if (data.length === 1) {
      message.warning('请不要删除信息!');
      return;
    }
    const index = data.findIndex(item => record.key === item.key);
    data.splice(index, 1);
    const array = _.cloneDeep(data);
    this.setState(
      {
        data: array,
      },
      () => {
        this.props.onSaveTableBox(data);
      }
    );
  };

  handleCopy = record => {
    const { data } = this.state;
    const { boxLimit } = this.props;
    if (data && boxLimit && data.length >= boxLimit) {
      message.error('已达最大箱单信息数量');
      return;
    }
    let copyValue;
    if (this.columns.filter(item => item.key === 'length').length > 0) {
      const key = parseInt(data[data.length - 1].key.split('box')[1]) + 1;
      copyValue = {
        boxWeight: record.boxWeight,
        height: record.height,
        length: record.length,
        predictVolumeWeight: record.predictVolumeWeight,
        tagNumber:
          computedNumber(record.tagNumber) !== undefined && record.tagNumber !== undefined
            ? computedNumber(record.tagNumber)
            : record.tagNumber,
        width: record.width,
        key: `box${+key}`,
      };
    } else {
      const key = parseInt(data[data.length - 1].key.split('declare')[1]) + 1;
      copyValue = {
        key: `declare${+key}`,
        chineseName: record.chineseName,
        declareUnitPrice: record.declareUnitPrice,
        englishName: record.englishName,
        hsCode: record.hsCode,
        images: record.images,
        imageUrl:
          record?.imageUrl !== undefined || record?.imageUrl !== '' ? record.imageUrl : undefined,
        linkUrl: record.linkUrl,
        weight: record.weight,
        brand: record.brand,
        brandType: record.brandType,
        netWeight: record.netWeight,
        productSku: record.productSku,
        purchasePrice: record.purchasePrice,
        purpose: record.purpose,
        quantity: record.quantity,
        remark: record.remark,
        saleLinkUrl: record.saleLinkUrl,
        tagNumber:
          computedNumber(record.tagNumber) !== undefined && record.tagNumber !== undefined
            ? computedNumber(record.tagNumber)
            : record.tagNumber,
        texture: record.texture,
        type: record.type,
      };
    }
    this.setState(
      {
        data: [...data, copyValue],
      },
      () => {
        this.props.onSaveTableBox([...data, copyValue]);
      }
    );
  };

  // 绑定cell
  onRefCell = ref => {
    this.EditableCells = ref;
  };

  // 重置表单
  onResetForm = () => {
    this.EditableCells.resetForm();
  };

  // 将错误信息发送到父组件
  sendError = error => {
    if (error !== undefined) {
      let { errors } = error;
      this.props.sendError(errors[0].message);
    } else {
      this.props.sendError('');
    }
  };

  // 列表选中取消
  onTableSelectChange = (selectedRowKeys, selectedRows) => {
    this.setState({
      selectedRowKeys: selectedRowKeys,
      selectedRows: selectedRows,
    });
    this.props.onTableSelectChange(selectedRowKeys, selectedRows);
  };

  render() {
    const { selectedRowKeys, data } = this.state;
    const components = {
      body: {
        row: EditableFormRow,
        cell: EditableCell,
      },
    };

    const rowSelection = { onChange: this.onTableSelectChange, selectedRowKeys: selectedRowKeys };
    const { title } = this.props;
    this.columns = [
      ...this.props.columns,
      {
        title: '操作',
        dataIndex: '操作',
        width: '150px',
        fixed: 'right',
        render: (text, record) => {
          return (
            <div>
              <a style={{ marginRight: '10px' }} onClick={() => this.handleCopy(record)}>
                复制
              </a>
              <Popconfirm title="确认删除吗?" onConfirm={() => this.handleDelete(record)}>
                <a style={{ marginRight: '10px' }}>删除</a>
              </Popconfirm>
            </div>
          );
        },
      },
    ];

    const columns = this.columns.map(col => {
      if (!col.editable) {
        return col;
      }
      return {
        ...col,
        align: 'center',
        onCell: record => ({
          record,
          inputType: col.inputType,
          dataIndex: col.dataIndex,
          title: col.titleName,
          editable: col.editable,
          required: col.required,
          pattern: col.pattern,
          max: col?.max,
          handleSave: this.handleSave,
          onRef: this.onRefCell,
          beforeUpload: this.beforeUpload,
          handleRemove: this.handleRemove,
          showModelImage: this.showModelImage,
          sendError: this.sendError,
          bubbleCoefficient: this.props.bubbleCoefficient,
        }),
      };
    });

    return (
      <Table
        // rowKey={(record, index) => record.key}
        rowKey="key"
        rowSelection={title == '申报信息' ? rowSelection : ''}
        components={components}
        bordered
        dataSource={this.state.data}
        columns={columns}
        rowClassName="editable-row"
        pagination={false}
        loading={this.state.uploadLoading}
        scroll={{ x: 'max-content' }}
        footer={this.state.footer || null}
      />
    );
  }
}

export default EditableTable;
