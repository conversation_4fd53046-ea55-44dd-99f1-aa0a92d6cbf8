import React, { useEffect, useState } from 'react';
import { EditableProTable } from '@ant-design/pro-components';
import './index.less';
import { useDebounceFn } from 'ahooks';

const EditableOldFormItem = props => {
  const {
    columns,
    value,
    onChange,
    creatorButtonText,
    getData,
    isEdit,
    showRecordCreator = true, // 用于展示表格下新增按钮 true的展示 false不展示
  } = props;
  const [editableKeys, setEditableRowKeys] = useState(value?.map(item => item?.tableKey) ?? []);
  const [dataSource, setDataSource] = useState(value ?? []);

  useEffect(() => {
    if (value) {
      setDataSource(value);
      setEditableRowKeys(value?.map(item => item?.tableKey));
    } else {
      setDataSource([]);
      setEditableRowKeys([]);
    }
  }, [value]);

  const triggerChange = recordList => {
    if (onChange) {
      onChange(recordList);
      if (getData) getData(recordList);
    }
  };

  const { run } = useDebounceFn(
    value => {
      triggerChange(value);
    },
    { wait: 250 }
  );

  return (
    <EditableProTable
      rowKey="tableKey"
      rowClassName={'editable-row'}
      //   className='editable'
      loading={false}
      columns={columns}
      value={dataSource}
      onChange={setDataSource}
      toolBarRender={false}
      recordCreatorProps={
        isEdit || !showRecordCreator
          ? false
          : {
              newRecordType: 'dataSource',
              record: () => ({ tableKey: (Math.random() * 1000000).toFixed(0) }),
              creatorButtonText,
            }
      }
      editable={{
        type: 'multiple',
        editableKeys,
        actionRender: (row, config, defaultDoms) => {
          return isEdit ? null : [defaultDoms.delete];
        },
        // onSave: async (rowKey, data, row) => {
        //   //   await waitTime(2000);
        // },
        onValuesChange: (record, recordList) => {
          // triggerChange(recordList);
          run(recordList);
        },
        onChange: setEditableRowKeys,
      }}
    />
  );
};

export default EditableOldFormItem;
