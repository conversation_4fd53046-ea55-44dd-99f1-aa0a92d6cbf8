import React from 'react';
import { Link } from 'umi';

type Props = {
  href: string;
  children: any;
  route?: boolean;
};
/**
 *
 * @returns
 */
const Index = ({ href, children, route }: Props) => {
  return (
    <>
      {route ? (
        <Link to={href}>{children}</Link>
      ) : (
        <a href={href} target={'_blank'}>
          {children}
        </a>
      )}
    </>
  );
};

export default Index;
