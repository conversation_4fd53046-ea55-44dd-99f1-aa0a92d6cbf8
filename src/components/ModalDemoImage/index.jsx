import { Modal, Space, Image } from 'antd';
import { useState, useEffect, useImperativeHandle } from 'react';
import { setBase64ImageWaterMark } from '@/utils/utils';
import { formatMessage } from 'umi-plugin-react/locale';
import DemoMainLand from '@/assets/demoMainLand.jpeg';
import DemoOverseas from '@/assets/demoOverseas.jpeg';
import DemoUserMandate from '@/assets/userMandateAttachDemo.png';
import DemoGangaotai from '@/assets/gangaotai.jpg';

const handleDemoTipMessage = ({ customerType, personOrLegal, area }) => {
  const areaEnum = {
    0: '大陆',
    1: '境外',
    3: '港澳台',
    default: '境外',
  };

  const messages = {
    person: {
      3: {
        0: '大陆居民身份证(正反面)加盖企业电子章/鲜章',
        3: '港澳台居民身份证(正反面)加盖企业电子章/鲜章',
        default: '本国居民身份证(正反面)+护照(正反面)加盖企业电子章/鲜章',
      },
      default: {
        0: '大陆居民身份证(正反面)加盖企业电子章/鲜章',
        3: '港澳台居民身份证(正反面)加盖企业电子章/鲜章',
        default: '本国居民身份证(正反面)+护照(正反面)，均需加盖企业电子章/鲜章',
      },
    },
    default: {
      3: {
        0: '大陆居民身份证(正反面)+港澳台通行证(正反面)，均需加盖企业电子章/鲜章',
        3: '港澳台居民身份证(正反面)，加盖企业电子章/鲜章',
        default: '本国居民身份证(正反面)+护照(正反面)，均需加盖企业电子章/鲜章',
      },
      default: {
        0: '大陆居民身份证(正反面)+护照(正反面)，均需加盖企业电子章/鲜章',
        3: '港澳台居民身份证(正反面)+护照(正反面)，均需加盖企业电子章/鲜章',
        default: '本国居民身份证(正反面)+护照(正反面)，均需加盖企业电子章/鲜章',
      },
    },
  };

  const personOrLegalMessages = messages[personOrLegal] || messages.default;
  const customerTypeMessages = personOrLegalMessages[customerType] || personOrLegalMessages.default;
  const message = customerTypeMessages[area] || customerTypeMessages.default;
  const areaText = areaEnum[area] || areaEnum?.default;

  return (
    <Space direction="vertical">
      <span>
        ③{customerType === 3 ? formatMessage({ id: '港澳台' }) : formatMessage({ id: '境外' })}
        {formatMessage({ id: '企业' })}
        {personOrLegal === 'person'
          ? formatMessage({ id: '经办人' })
          : formatMessage({ id: '法人' })}
        {formatMessage({ id: '证件要求' })}
        <span style={{ color: 'red' }}>
          ({formatMessage({ id: '请根据实际对应提供相关证件' })})
        </span>
      </span>
      <span>
        {formatMessage({ id: areaText })}
        {personOrLegal === 'person'
          ? formatMessage({ id: '经办人' })
          : formatMessage({ id: '法人' })}
        :{formatMessage({ id: message })}
      </span>
      {customerType == 1 && personOrLegal != 'person' && area == 1 && (
        <span style={{ color: 'red' }}>
          ({formatMessage({ id: '注' })}:{formatMessage({ id: '本地企业' })}+
          {formatMessage({ id: '本地法人' })}，{formatMessage({ id: '只需提供本国身份证' })}(
          {formatMessage({ id: '正反面' })})，{formatMessage({ id: '如' })}:
          {formatMessage({ id: '韩国企业' })}-{formatMessage({ id: '韩国法人' })}{' '}
          {formatMessage({ id: '只需' })}
          {formatMessage({ id: '提供本国居民身份证' })})
        </span>
      )}
    </Space>
  );
};

const handleImageDemoByType = ({ customerType, personOrLegal, area }) => {
  const urlEnum = {
    person: {
      3: {
        0: DemoMainLand,
        3: DemoMainLand,
        default: DemoOverseas,
      },
      default: {
        1: DemoOverseas,
        default: DemoMainLand,
      },
    },
    default: {
      3: {
        0: DemoOverseas,
        3: DemoMainLand,
        1: DemoOverseas,
        default: DemoOverseas,
      },
      default: {
        default: DemoOverseas,
      },
    },
  };

  const personOrLegalUrl = urlEnum[personOrLegal] || urlEnum.default;
  const customerTypeUrl = personOrLegalUrl[customerType] || personOrLegalUrl.default;
  const url = customerTypeUrl[area] || customerTypeUrl.default;

  return <Image src={url} width={'100%'} />;
};

const ModalDemoImage = props => {
  const { modalRef, width = 520 } = props;
  const [blobUrl, setBlobUrl] = useState(null);
  const [open, setOpen] = useState(false);
  const [needWater, setNeedWater] = useState(false);
  const [paramsInfo, setParamsInfo] = useState({});

  useImperativeHandle(modalRef, () => ({
    open: ({ url, water = false, params }) => {
      setNeedWater(water);
      setParamsInfo({ desc: true, ...paramsInfo, ...params, url });
      setOpen(true);
    },
  }));

  const onCancel = () => {
    setOpen(false);
    setBlobUrl(null);
    setNeedWater(false);
    setParamsInfo({});
  };

  return (
    <Modal width={width} open={open} title={null} footer={null} onCancel={onCancel}>
      {paramsInfo?.desc ? (
        <div>
          {handleImageDemoByType({ ...paramsInfo })}
          {/* <img alt="example" style={{ width: '100%', height: '100%' }} src={blobUrl} /> */}
          {paramsInfo?.desc && (
            <Space direction="vertical">
              <span>【说明】</span>
              <span>①证件彩印/黑白均可</span>
              <span>②正反面均在同一纸张，加盖的企业印章必须为鲜章/电子章，且需加盖在证件照上</span>
              {handleDemoTipMessage({ ...paramsInfo })}
            </Space>
          )}
        </div>
      ) : (
        <div>{paramsInfo?.url && <Image src={paramsInfo?.url} width={'100%'} />}</div>
      )}
    </Modal>
  );
};

export default ModalDemoImage;
