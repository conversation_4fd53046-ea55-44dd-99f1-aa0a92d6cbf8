import { Modal } from 'antd';

const ModalImage = props => {
  const { open, onCancel, url, width = 520 } = props;
  const imgTypeList = ['jpg', 'jpeg', 'png', 'gif'];
  const hasImgType = imgTypeList.some(type => url?.includes(type));
  return (
    <Modal width={width} open={open} title={null} footer={null} onCancel={onCancel}>
      {hasImgType ? (
        <img alt="example" style={{ width: '100%', height: '100%' }} src={url} />
      ) : (
        <div>该文件格式不支持预览</div>
      )}
    </Modal>
  );
};

export default ModalImage;
