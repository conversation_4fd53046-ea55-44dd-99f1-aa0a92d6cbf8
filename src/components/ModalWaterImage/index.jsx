import { Modal } from 'antd';
import { useState, useEffect, useImperativeHandle } from 'react';
import { setBase64ImageWaterMark } from '@/utils/utils';

const ModalWaterImage = props => {
  const { modalRef, width = 520 } = props;
  const [blobUrl, setBlobUrl] = useState(null);
  const [open, setOpen] = useState(false);
  const [needWater, setNeedWater] = useState(false);

  useImperativeHandle(modalRef, () => ({
    open: ({ url, water = false }) => {
      setNeedWater(water);
      setBlobUrl(url);
      setOpen(true);
    },
  }));

  const onCancel = () => {
    setOpen(false);
    setBlobUrl(null);
    setNeedWater(false);
  };

  return (
    <Modal width={width} open={open} title={null} footer={null} onCancel={onCancel}>
      {blobUrl ? (
        <div className="relative">
          <img alt="example" style={{ width: '100%', height: '100%' }} src={blobUrl} />
          {needWater && (
            <span
              className="absolute top-1/2"
              style={{
                left: '37%',
                fontSize: '20px',
                transform: 'rotate(-35deg)',
                letterSpacing: '4px',
                color: 'red',
              }}
            >
              仅限燕文物流使用
            </span>
          )}
        </div>
      ) : (
        <div>Loading...</div>
      )}
    </Modal>
  );
};

export default ModalWaterImage;
