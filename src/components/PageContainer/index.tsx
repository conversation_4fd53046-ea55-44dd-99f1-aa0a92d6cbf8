import React from 'react';
import { PageContainer } from '@ant-design/pro-components';
import { Button, Result } from 'antd';
// import { router } from 'umi';

type PageContainerProps = React.ComponentProps<typeof PageContainer> & { [key: string]: any };

const PageContainerComponent: React.FC<PageContainerProps> = props => {
  const { children, pageError, token } = props;
  return (
    <PageContainer {...props} token={{ ...token }}>
      {pageError ? (
        <Result
          status="error"
          title="发生了系统错误"
          extra={[
            // <Button type="primary" key="console">
            //   Go Console
            // </Button>,
            <Button
              key="buy"
              onClick={() => {
                window.location.reload();
              }}
            >
              刷新
            </Button>,
          ]}
        />
      ) : (
        children
      )}
    </PageContainer>
  );
};

export default PageContainerComponent;
