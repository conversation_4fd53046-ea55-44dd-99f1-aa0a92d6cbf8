import React, { useState, useEffect } from 'react';
// @ts-ignore
import styles from './index.less';
import { Breadcrumb, Space } from 'antd';
import { changeBreadcrumb } from '@/utils/utils';

type Props = {
  children?: React.ReactNode;
  pageTitleInfo?: any;
  col?: any;
};

const PageHeaderBreadcrumb = (props: Props) => {
  const {
    children,
    pageTitleInfo: { id },
    col,
  } = props;

  const [breadcrumbData, setBreadcrumbData] = useState([]);

  useEffect(() => {
    if (id) {
      setBreadcrumbData(changeBreadcrumb(id));
    }
  }, [id]);

  const classNameStyle = col ? 'flex flex-col justify-around' : 'flex flex-row justify-between';

  return breadcrumbData && breadcrumbData?.length > 0 ? (
    <section className={styles.indexpageHeader}>
      <div className={styles.antbreadcrumb}>
        {id?.includes('.') ? (
          <div className={classNameStyle}>
            <Space direction={col ? 'vertical' : 'horizontal'}>
              <Breadcrumb className="cursor-default ">
                {breadcrumbData &&
                  breadcrumbData.map((item, index) => (
                    <Breadcrumb.Item key={index}>{item}</Breadcrumb.Item>
                  ))}
              </Breadcrumb>
              {children}
            </Space>
          </div>
        ) : (
          <>{children}</>
        )}
      </div>
    </section>
  ) : (
    <></>
  );
};

export default PageHeaderBreadcrumb;
