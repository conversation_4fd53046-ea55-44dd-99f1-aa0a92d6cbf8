import React, { useState } from 'react';
import { Button, Col, Input, message, Popover, Row, Tabs, Typography } from 'antd';
import { LeftOutlined, RightOutlined } from '@ant-design/icons';
import { useDebounceFn, useUpdateEffect } from 'ahooks';

const { Text } = Typography;

/**
 * 根据给定的函数体生成一个带有正确语法的markdown代码块的函数注释。
 *
 * @param {object} props - 包含以下属性的props对象：
 *   - {ReactNode} children: 渲染在PopoverTabs组件内部的子组件。
 *   - {string} value: PopoverTabs组件的当前值。
 *   - {function} onChange: 当PopoverTabs组件的值发生变化时调用的函数。用于给Form使用
 *   - {function} onSelectOnChange: 当PopoverTabs组件的选中值发生变化时调用的函数。
 * @return {ReactNode} 渲染的PopoverTabs组件。
 */
const PopoverTabs = ({
  value,
  onChange,
  onSelectOnChange,
  data,
  selectOnFocus,
  countryDisabled,
  form,
}) => {
  const renderTabsItemParent = data => <Row gutter={20}>{renderTabsItemChildren(data)}</Row>;
  /**
   * The function `initialArray` maps an array of objects, adding a `label` property and a `children`
   * property with a JSX element as its value.
   */
  const initialArray = () => {
    return (
      data?.map(item => ({
        ...item,
        label: item?.key,
        value: item?.value?.map(val => ({ ...val, canSelect: true })),
        children: renderTabsItemParent(item?.value?.map(val => ({ ...val, canSelect: true }))),
      })) ?? []
    );
  };
  const handleChange = item => {
    if (onChange) {
      onChange(item?.productNumber);
      setShowValue(`${item?.productNumber}-${item?.productCnName}`);
      setIsInput(false);
      if (onSelectOnChange) onSelectOnChange(item?.productNumber, item);
      handleOpenChange(false);
    }
  };

  const renderTabsItemChildren = dataList => {
    return dataList.map(item => (
      <Col flex={'210px'} style={{ cursor: 'pointer', textAlign: 'left' }}>
        <Text
          disabled={!item?.canSelect}
          ellipsis
          onClick={() => (item?.canSelect ? handleChange(item) : null)}
        >
          {item?.productNumber}-{item?.productCnName}
        </Text>
      </Col>
    ));
  };
  const [open, setOpen] = useState(false);
  const [activeKey, setActiveKey] = useState(null);
  const [tabList, setTabList] = useState(initialArray());
  const [showValue, setShowValue] = useState();
  const [isInput, setIsInput] = useState(false);

  useUpdateEffect(() => {
    if (value) {
      if (tabList.length > 0) {
        handleSelectActiveKey();
      }
    } else {
      if (isInput) return;
      setActiveKey(data?.[0]?.key);
      data?.forEach(item => {
        const match = item?.value?.find(val => val?.productNumber == value);
        if (match) {
          setShowValue(`${match?.productNumber}-${match?.productCnName}`);
        }
      });
      setShowValue(value);
    }
  }, [value, tabList, isInput]);

  useUpdateEffect(() => {
    if (data) {
      setTabList(initialArray());
    }
  }, [data]);

  const handleSelectActiveKey = () => {
    if (isInput) return;
    data?.forEach(item => {
      const match = item?.value?.find(val => val?.productNumber == value);
      if (match) {
        setActiveKey(item?.key);
        setShowValue(`${match?.productNumber}-${match?.productCnName}`);
      }
    });
  };

  const handleOpenChange = newOpen => {
    const countryId = form.getFieldValue('countryId');
    const channelId = form.getFieldValue('channelId');
    if (!countryId) return message.warn('请先选择目的地！');
    setOpen(newOpen);
    // 暂时去掉关闭掉tabs时不清空
    if (!newOpen) {
      setIsInput(false);
      setTabList(initialArray());
      data?.forEach(item => {
        const match = value
          ? item?.value?.find(val => val?.productNumber == value)
          : item?.value?.find(val => `${val?.productNumber}-${val?.productCnName}` == showValue);
        if (match) {
          setActiveKey(item?.key);
          setShowValue(`${match?.productNumber}-${match?.productCnName}`);
        } else if (!value && !channelId) {
          setShowValue(null);
        }
      });
    }
  };

  const handleClickIcon = type => {
    const index = data?.findIndex(item => item.key === activeKey);
    switch (type) {
      case 'left':
        setActiveKey(data?.[index - 1]?.key);
        break;
      case 'right':
        setActiveKey(data?.[index + 1]?.key);
        break;
    }
  };

  const content = () => {
    return (
      <Tabs
        style={{ width: 620 }}
        activeKey={activeKey}
        items={tabList}
        onChange={setActiveKey}
        tabBarExtraContent={{
          left: (
            <Button
              type="text"
              disabled={activeKey === data?.[0]?.key}
              icon={<LeftOutlined />}
              onClick={() => handleClickIcon('left')}
            />
          ),
          right: (
            <Button
              type="text"
              disabled={activeKey === data?.[data.length - 1]?.key}
              icon={<RightOutlined />}
              onClick={() => handleClickIcon('right')}
            />
          ),
        }}
      />
    );
  };

  const { run } = useDebounceFn(
    value => {
      setIsInput(true);
      const newData = tabList?.map(item => {
        const newValue = item.value.map(values => {
          const canSelect =
            value === ''
              ? true
              : `${values?.productNumber}-${values?.productCnName}`
                  .toLowerCase()
                  .includes(value.toLowerCase()) && value
              ? true
              : false;
          return {
            ...values,
            canSelect: canSelect,
          };
        });

        return {
          ...item,
          value: newValue,
          children: renderTabsItemParent(newValue),
        };
      });
      const searchTabListData = tabList
        .flatMap(item => item.value)
        .map(item => ({
          key: item.parentKey,
          productCnName: item.productCnName,
          productNumber: item.productNumber,
        }));
      if (value && value !== '') {
        const matchItem = searchTabListData.find(val =>
          `${val?.productNumber}-${val?.productCnName}`.toLowerCase().includes(value.toLowerCase())
        );
        if (matchItem) {
          setActiveKey(matchItem?.key);
        } else if (!matchItem && !value) {
          setActiveKey(data?.[0]?.key);
        }
      }
      setTabList(newData);
    },
    { wait: 250 }
  );

  const handleChangeInput = e => {
    const { value } = e.target;
    setShowValue(value);
    run(value);
    // if (!value) {
    //   handleChange();
    // }
  };
  return (
    <>
      <Popover
        content={content()}
        title={null}
        trigger="click"
        placement="bottomLeft"
        open={open}
        onOpenChange={handleOpenChange}
        overlayStyle={{ paddingTop: '8.25px' }}
      >
        <Input
          value={showValue}
          onFocus={selectOnFocus}
          disabled={countryDisabled}
          onChange={handleChangeInput}
          allowClear
          placeholder="请选择产品"
        />
      </Popover>
    </>
  );
};

export default PopoverTabs;
