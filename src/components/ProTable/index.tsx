import React, { useRef, useState, useImperativeHandle } from 'react';
// @ts-ignore
import { ActionType } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import { PaginationProps } from 'antd/es/pagination';
type props = {
  tableRef?: any;
  rowKey?: string | ((record: any, index: any) => string);
  dataSource?: object[];
  columns?: object[];
  setData?: object;
  pageSize?: number;
  bordered?: boolean;
  searchConfig?: object[];
  headerTitle?: string | React.ReactNode;
  total?: number;
  toolBarRender?: any;
  toolbar?: any;
  options?: any;
  onSelectRow?: (arg0?: string[], arg1?: string[]) => void;
  paginationProps?: PaginationProps;
  loading?: boolean;
  rowSelection?: any;
  [key: string]: any;
};

const ProTableList: React.FC<props> = (props: props) => {
  const {
    tableRef,
    columns,
    dataSource,
    pageSize,
    bordered,
    headerTitle,
    total,
    onSelectRow,
    options = {},
    paginationProps,
    rowKey,
    loading = false,
    rowSelection,
    ...reset
  } = props;
  const { toolBarRender: SearchForm } = props;
  const actionRef = useRef<ActionType>();

  columns.map((res: any) => {
    res.align = res?.align ?? 'center';
  });

  useImperativeHandle(tableRef, () => ({
    resetSelectKeys: () => {
      actionRef.current.reset();
    },
  }));

  // 多选框的选择值
  const [selectedKeys, setSelectedKeys] = useState([]);
  // 列表复选框选中变化
  const onSelectChange = (selectedRowKeys: any[], selectedRows: any[]): void => {
    setSelectedKeys(selectedRowKeys);
    onSelectRow(selectedRowKeys, selectedRows);
  };

  const rowSelectionProps = {
    selectedRowKeys: selectedKeys,
    onChange: onSelectChange,
  };

  return (
    <ProTable
      rowKey={rowKey}
      rowSelection={rowSelection !== undefined ? rowSelection : rowSelectionProps}
      dataSource={dataSource}
      columns={columns}
      // scroll={{ x: 'max-content' }}
      actionRef={actionRef}
      cardBordered
      bordered={bordered}
      loading={loading}
      // 搜索表单自定义
      search={false}
      options={typeof options == 'object' ? { reload: true, density: false, ...options } : options}
      size="small"
      pagination={{
        pageSize: pageSize,
        total: total,
        defaultPageSize: 10,
        showSizeChanger: true,
        ...paginationProps,
      }}
      dateFormatter="string"
      headerTitle={headerTitle}
      // 自定义添加operateBar
      toolBarRender={() => [!!SearchForm && <SearchForm />]}
      {...reset}
    />
  );
};
// ProTableList.defaultProps = {
//   dataSource: [],
//   columns: [],
//   setData: {},
//   pageSize: 10,
//   bordered: true,
//   searchConfig: [],
//   headerTitle: '',
//   total: 0,
//   // SearchForm:null
//   onSelectRow: () => {},
//   toolbar: null,
//   options: {},
// }

export default ProTableList;
