import React from 'react';
import { Modal, Space } from 'antd';
import QRCode from 'qrcode.react';
// @ts-ignore
import ywLogo from '@/assets/ywLogo.jpg';

const QRCodeModal = ({ open, onCancel, url, title = null, params = null }) => {
  return (
    <Modal
      open={open}
      title={title}
      footer={null}
      onCancel={onCancel}
      style={{ top: 180 }}
      width={380}
      bodyStyle={{
        display: 'flex',
        flexDirection: 'column',
        justifyItems: 'center',
        alignItems: 'center',
      }}
    >
      <QRCode
        size={200}
        value={url}
        renderAs="canvas"
        bgColor="white"
        fgColor="black"
        imageSettings={{
          src: ywLogo,
          height: 40,
          width: 40,
          excavate: false,
        }}
      />
      <Space direction="vertical" align="center" className="text-center w-full mt-3">
        <p className="m-0" style={{ color: '#52c41a' }}>
          {params?.name}
        </p>
        <p className="m-0">{params?.phone}</p>
      </Space>
    </Modal>
  );
};

export default QRCodeModal;
