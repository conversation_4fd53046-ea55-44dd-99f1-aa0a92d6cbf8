import React, { useState, useImperativeHandle, useRef } from 'react';
import { Modal, message } from 'antd';
import ProTableList from '@/components/ProTable';

type RowKeyType = string | ((record: any, index: any) => string);

type Props = {
  modalRef: React.RefObject<any>;
  dispatch: Function;
  onCancel: Function;
  url: string;
  rowKey: RowKeyType;
  columns: Array<any>;
  title: string;
};

const RecipientAddressModal = ({
  modalRef,
  dispatch,
  onCancel,
  url,
  rowKey,
  columns,
  title,
}: Props) => {
  const [open, setOpen] = useState(false);
  const [dataList, setDataList] = useState([]);
  const [selectResult, setSelectResult] = useState();

  const tableRef = useRef<any>();

  useImperativeHandle(modalRef, () => ({
    open: params => {
      initialFunc(params);
    },
  }));

  const initialFunc = params => {
    dispatch({
      type: url,
      payload: params,
      callback: response => {
        if (response.success) {
          setDataList(response.data?.data);
          setOpen(true);
        } else {
          handleCancel();
        }
      },
    });
  };

  const handleCancel = (value?: any) => {
    setOpen(false);
    setSelectResult(undefined);
    tableRef.current?.resetSelectKeys();
    if (value) {
      onCancel(value);
    }
  };

  const handleSubmit = () => {
    if (selectResult) {
      handleCancel(selectResult);
    } else {
      message.warning('请选择地址');
    }
  };

  const rowSelection = {
    onChange: (_: React.Key[], selectedRows: any[]) => {
      setSelectResult(selectedRows[0]);
    },
  };

  return (
    <Modal
      title={title}
      width="50%"
      open={open}
      onCancel={() => handleCancel()}
      okButtonProps={{
        disabled: !selectResult,
      }}
      onOk={handleSubmit}
      destroyOnClose
    >
      <ProTableList
        rowKey={rowKey}
        // loading={getListLoading}
        columns={columns}
        dataSource={dataList}
        search={false}
        toolBarRender={false}
        rowSelection={{
          type: 'radio',
          ...rowSelection,
        }}
      />
    </Modal>
  );
};

export default RecipientAddressModal;
