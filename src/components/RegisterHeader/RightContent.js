import React, { PureComponent } from 'react';
import { Button, Row, Divider } from 'antd';
import router from 'umi/router';
import { formatMessage } from 'umi-plugin-react/locale';

export default class RightContent extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      hideLogin: false,
    };
  }

  componentDidMount() {
    const { location } = this.props;
    const target = location.query?.target;
    if (target) {
      this.setState({
        hideLogin: true,
      });
    }
  }

  render() {
    let rightStyle = { float: 'right', lineHeight: '64px' };
    return (
      <div style={rightStyle}>
        {/* <Button
          type="text"
          href="https://portal.yw56.com.cn/demonstrator/index.html"
          target="_blank"
        >
          注册演示
        </Button>
        <Divider type="vertical" /> */}
        <Button
          style={{ display: this.state.hideLogin ? 'none' : '' }}
          type="text"
          onClick={() => router.push(`/user/login`)}
        >
          {formatMessage({ id: '登录' })}
        </Button>
      </div>
    );
  }
}
