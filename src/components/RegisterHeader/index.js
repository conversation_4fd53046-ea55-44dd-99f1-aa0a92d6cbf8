import React, { PureComponent } from 'react';
import { Select } from 'antd';
import Link from 'umi/link';
import Debounce from 'lodash-decorators/debounce';
import styles from './index.less';
import RightContent from './RightContent';
import Logo from '../../assets/logo.png';
import { formatMessage } from 'umi-plugin-react/locale';

class RegisterHeader extends PureComponent {
  state = {
    isShow: false,
  };

  componentWillUnmount() {
    this.triggerResizeEvent.cancel();
  }
  /* eslint-disable*/
  @Debounce(600)
  triggerResizeEvent() {
    // eslint-disable-line
    const event = document.createEvent('HTMLEvents');
    event.initEvent('resize', true, false);
    window.dispatchEvent(event);
  }
  toggle = () => {
    const { collapsed, onCollapse } = this.props;
    onCollapse(!collapsed);
    this.triggerResizeEvent();
  };

  render() {
    return (
      <div className={styles.header}>
        <Link className={styles.logo} key="logo">
          <img src={Logo} alt="logo" onClick={() => window.open('https://www.yw56.com.cn/')} />
          <span style={{ color: '#7cb830' }}>
            &nbsp;&nbsp; {formatMessage({ id: '客户服务中心' })}&nbsp;&nbsp;
          </span>
        </Link>
        <RightContent {...this.props} />
      </div>
    );
  }
}
export default RegisterHeader;
