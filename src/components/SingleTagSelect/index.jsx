import React, { Component } from 'react';
import PropTypes from 'prop-types';
import classNames from 'classnames';
import { DownOutlined, UpOutlined } from '@ant-design/icons';
import { Tag } from 'antd';

import styles from './index.less';

const { CheckableTag } = Tag;
const TagSelectOption = ({ children, checked, onChange, value }) => (
  <CheckableTag checked={checked} key={value} onChange={state => onChange(value, state)}>
    {children}
  </CheckableTag>
);

TagSelectOption.isTagSelectOption = true;

class SingleTagSelect extends Component {
  static propTypes = {
    // eslint-disable-next-line react/forbid-prop-types
    actionsText: PropTypes.object,
    hideCheckAll: PropTypes.bool,
  };

  static defaultProps = {
    hideCheckAll: true,
    actionsText: {
      expandText: 'Expand',
      collapseText: 'Collapse',
      selectAllText: 'All',
    },
  };

  constructor(props) {
    super(props);
    this.state = {
      expand: false,
      value: props.value || props.defaultValue || [],
      checked: props.checked,
    };
  }
  componentDidMount() {}
  static getDerivedStateFromProps(nextProps) {
    if ('value' in nextProps) {
      return { value: nextProps.value || [] };
    }
    return null;
  }

  onChange = (value, index) => {
    const { onChange } = this.props;
    if (!('value' in this.props)) {
      this.setState({ value });
    }
    if (onChange) {
      onChange(value, index);
    }
  };

  onSelectAll = checked => {
    let checkedTags = [];
    if (checked) {
      checkedTags = this.getAllTags();
    }
    this.onChange(checkedTags);
  };

  getAllTags() {
    let { children } = this.props;
    children = React.Children.toArray(children);
    const checkedTags = children
      .filter(child => this.isTagSelectOption(child))
      .map(child => child.props.value);
    return checkedTags || [];
  }

  handleTagChange = (value, checked, index) => {
    const { value: StateValue } = this.state;
    let checkedTags = [...StateValue];
    //----tag单选处理
    checkedTags = [value];
    //-----tag多选处理
    // const index = checkedTags.indexOf(value)
    // if (checked && index === -1) { //没有这个东西
    // 	checkedTags.push(value)
    // } else if (!checked && index > -1) {
    // 	checkedTags.splice(index, 1)
    // }
    this.onChange(checkedTags, index);
  };

  handleExpand = () => {
    const { expand } = this.state;
    this.setState({
      expand: !expand,
    });
  };

  isTagSelectOption = node =>
    node &&
    node.type &&
    (node.type.isTagSelectOption || node.type.displayName === 'TagSelectOption');

  render() {
    const { value, expand } = this.state;
    const {
      children,
      hideCheckAll,
      className,
      style,
      expandable,
      actionsText,
      checked,
      customExpanded,
    } = this.props;
    const checkedAll = this.getAllTags().length === value.length;
    const { expandText = 'Expand', collapseText = 'Collapse', selectAllText = 'All' } =
      actionsText === null ? {} : actionsText;

    const cls = classNames(styles.tagSelect, className, {
      [styles.hasExpandTag]: expandable,
      [styles.expanded]: expand,
      [styles.customExpanded]: customExpanded,
    });

    return (
      <div className={cls} style={style}>
        {hideCheckAll ? null : (
          <CheckableTag checked={checkedAll} key="tag-select-__all__" onChange={this.onSelectAll}>
            {selectAllText}
          </CheckableTag>
        )}
        {value &&
          React.Children.map(children, (child, index) => {
            if (this.isTagSelectOption(child)) {
              return React.cloneElement(child, {
                key: `tag-select-${child.props.value}`,
                value: child.props.value,
                checked: value.indexOf(child.props.value) > -1 || checked,
                // eslint-disable-next-line no-shadow
                onChange: (value, checked) => {
                  this.handleTagChange(value, checked, index);
                },
              });
            }
            return child;
          })}
        {expandable && (
          <a className={styles.trigger} onClick={this.handleExpand}>
            {expand ? collapseText : expandText} {expand ? <UpOutlined /> : <DownOutlined />}
          </a>
        )}
      </div>
    );
  }
}

SingleTagSelect.Option = TagSelectOption;

export default SingleTagSelect;
