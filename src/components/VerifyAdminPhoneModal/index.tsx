import React, { useState, useRef, useImperativeHandle } from 'react';
import { Modal, Button, Row, Divider, Space, Input } from 'antd';
import {
  ProFormText,
  ProFormGroup,
  ProForm,
  ProFormInstance,
  ProFormSelect,
  ProFormTextArea,
  ProFormRadio,
} from '@ant-design/pro-components';
import { useCountDown } from 'ahooks';
import { customerPlatformSources } from '@/utils/commonConstant';
import { PlusOutlined } from '@ant-design/icons';

type Props = {
  dispatch: any;
  sendSmsFunc?: (callback?: () => void, types?: string) => void;
  modalRef: any;
  needLogout?: boolean;
  onSubmit: (values: any, callback?: () => void) => void;
  loading?: boolean;
  phoneTitle?: string;
  noSendSms?: boolean; // 不需要发送验证码
  onCancel?: () => void;
};

/**
 * 校验管理员手机号弹窗
 * @param
 * @returns
 */
const Index = ({
  dispatch,
  modalRef,
  needLogout = true,
  onSubmit,
  sendSmsFunc,
  loading = false,
  phoneTitle = '管理员手机号',
  noSendSms = false,
  onCancel,
}: Props) => {
  const formRef = useRef<ProFormInstance>();
  const inputRef = useRef<any>();
  const [title, setTitle] = useState<string | undefined>();
  const [open, setOpen] = useState(false);
  const [types, setTypes] = useState(); // 类型
  const [targetDate, setTargetDate] = useState<undefined | number>(); // 倒计时
  const [paramsInfo, setParamsInfo] = useState({}); // 参数信息
  const [managementPlatformItem, setManagementPlatformItem] = useState(''); // 经营平台
  const [countdown] = useCountDown({
    targetDate,
  });

  useImperativeHandle(modalRef, () => ({
    openModal: params => {
      setTimeout(() => {
        formRef.current?.setFieldsValue({
          ...params,
          mobile: params?.phone,
          managementPlatform:
            typeof params?.managementPlatform == 'string'
              ? params?.managementPlatform.split(',')
              : [],
        });
      }, 200);
      setTitle(params?.title);
      setTypes(params?.types);
      setParamsInfo({
        ...paramsInfo,
        ...params,
      });
      setOpen(true);
    },
  }));

  const sendSmsCode = () => {
    sendSmsFunc(() => {
      setTargetDate(Date.now() + 1000 * 60);
    }, types);
  };

  const handleCancel = () => {
    formRef.current?.resetFields();
    setOpen(false);
    setTargetDate(undefined);
    setTypes(undefined);
    setParamsInfo({});
    if (onCancel) {
      onCancel();
    }
  };

  // 退出登录
  const logout = () => {
    dispatch({
      type: 'login/logout',
    });
  };

  // 经营平台自定义输入
  const addItem = e => {
    e.preventDefault();
    let data = formRef.current?.getFieldsValue();
    if (managementPlatformItem !== '') {
      formRef.current?.setFieldsValue({
        managementPlatform: [...(data?.managementPlatform ?? []), managementPlatformItem],
      });
      setManagementPlatformItem('');
      setTimeout(() => {
        inputRef.current?.focus();
      }, 0);
    }
  };

  const handleSubmit = async values => {
    onSubmit(
      {
        ...paramsInfo,
        ...values,
        verifyPhone: true,
        types,
        managementPlatform: values?.managementPlatform?.join(','),
      },
      () => {
        if (needLogout) {
          logout();
        } else {
          handleCancel();
        }
      }
    );
  };

  return (
    <Modal title={title} open={open} onCancel={() => handleCancel()} footer={null}>
      <ProForm
        layout="horizontal"
        labelAlign="right"
        labelCol={{ flex: '150px' }}
        formRef={formRef}
        submitter={{
          render: (_, dom) => null,
        }}
        onFinish={handleSubmit}
      >
        <ProFormText
          name="mobile"
          width="md"
          label={phoneTitle}
          //   initialValue={userInfo.mobile}
          readonly
        />
        {title === '违禁品冻结' && (
          <>
            <ProFormText
              label="主营产品"
              width="md"
              name="mainProducts"
              rules={[
                {
                  required: true,
                  message: '请输入主营产品',
                },
                {
                  max: 50,
                  message: '最多输入50个字符',
                },
              ]}
              placeholder={'请输入主营产品'}
            />
            <ProFormSelect
              label="经营平台"
              width="md"
              name="managementPlatform"
              rules={[
                {
                  required: true,
                  message: '请选择经营平台',
                },
              ]}
              placeholder={'请选择经营平台'}
              fieldProps={{
                mode: 'tags',
                options: customerPlatformSources.map(item => ({
                  value: item,
                  label: item,
                })),
                tokenSeparators: [','],
                dropdownRender: menu => (
                  <>
                    {menu}
                    <Divider
                      style={{
                        margin: '8px 0',
                      }}
                    />
                    <Space
                      style={{
                        padding: '0 8px 4px',
                      }}
                    >
                      <Input
                        placeholder="请输入其他平台"
                        ref={inputRef}
                        value={managementPlatformItem}
                        onChange={event => setManagementPlatformItem(event.target.value)}
                        onKeyDown={e => {
                          e.stopPropagation();
                        }}
                      />
                      <Button type="text" icon={<PlusOutlined />} onClick={addItem}>
                        添加
                      </Button>
                    </Space>
                  </>
                ),
              }}
            />
            <ProFormTextArea
              label="申请原因"
              width="md"
              name="applyReason"
              rules={[
                {
                  required: true,
                  message: '请输入申请原因',
                },
                {
                  max: 200,
                  message: '最多输入200个字符',
                },
              ]}
              placeholder={'请输入申请原因'}
            />

            <ProFormRadio.Group
              name="hasAirBanItems"
              width="md"
              rules={[
                {
                  required: true,
                  message: '请选择是否涉及航空违禁品',
                },
              ]}
              label="是否涉及航空违禁品"
              options={[
                {
                  label: '否',
                  value: 0,
                },
                {
                  label: '是',
                  value: 1,
                },
              ]}
            />
          </>
        )}
        <ProFormGroup labelLayout="inline">
          <ProFormText
            label="验证码"
            name="smsCode"
            rules={[
              {
                required: true,
                message: '请输入验证码',
              },
              {
                pattern: /^\d{6}$/,
                message: '请输入正确验证码',
              },
            ]}
            placeholder="请输入验证码"
          />
          {!noSendSms && (
            <Button disabled={countdown !== 0} onClick={sendSmsCode}>
              {' '}
              {countdown === 0 ? '获取验证码' : `${Math.round(countdown / 1000)}s`}
            </Button>
          )}
        </ProFormGroup>
        <Row justify="space-around">
          <Button onClick={() => handleCancel()}>取消</Button>
          <Button htmlType="submit" type="primary" loading={loading}>
            提交
          </Button>
        </Row>
      </ProForm>
    </Modal>
  );
};
export default Index;
