import { openSignModal } from '@/utils/utils';
import { message } from 'antd';

interface SignUrlResult {
  signUrl?: string;
  contractName?: string;
}

interface AuthParams {
  contractType: number;
  viewContract?: boolean;
}

/**
 * 处理错误消息
 */
const handleErrorMessage = (result: any, contractType: number): string => {
  if (contractType === 0 && result.message?.includes('进行实名认证或纸质合同签约')) {
    return '请完成实名认证流程后再签署委托书（实名认证提交入口：用户管理-实名认证）';
  }
  return result.message;
};

/**
 * 获取签约URL
 */
const getSignUrl = async (dispatch: any, params: AuthParams): Promise<SignUrlResult> => {
  return new Promise((resolve, reject) => {
    dispatch({
      type: 'paymentAccount/getSignUrl',
      payload: params,
      callback: (result: any) => {
        if (result.success) {
          resolve(result.data);
        } else {
          const msg = handleErrorMessage(result, params.contractType);
          message.error(msg);
          reject(msg);
        }
      },
    });
  });
};

/**
 * 签约状态Hook
 * @param dispatch - dispatch函数
 * @param param - 签约参数
 */
export default function useAuthStatus(dispatch: any, param: AuthParams) {
  return new Promise(async (resolve, reject) => {
    try {
      // 获取预览URL
      const previewData = await getSignUrl(dispatch, param);
      // 打开签约模态框
      openSignModal({
        url: previewData.signUrl,
        contractName: previewData.contractName,
        closable: false,
        func: async (callback: (signUrl: string) => void) => {
          try {
            // 获取实际签约URL
            const signData = await getSignUrl(dispatch, { ...param, viewContract: false });
            callback(signData.signUrl);
          } catch (error) {
            reject(error);
          }
        },
      });

      resolve(previewData);
    } catch (error) {
      reject(error);
    }
  });
}
