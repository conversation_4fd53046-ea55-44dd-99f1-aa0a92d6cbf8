/**
 * @Description: 下载文件超时，需要实现循环下载
 * @MethodAuthor: dangh
 * @Date: 2023-10-18 10:30:20
 */
export default function useAutoCheckDownloadFile(dispatch: any, url: string, params: any) {
  let count = 0;
  let intervalTime = 1000;
  let timeoutId;
  // 发起重复检查的方法
  const requestCheckFunc = () =>
    new Promise((resolve, reject) => {
      const fetchDataAndCheckStatus = () => {
        dispatch({
          type: url,
          payload: params,
          callback: response => {
            if (response.success) {
              if (response.data === 1) {
                // 下载成功
                count = 0;
                intervalTime = 1000;
                if (timeoutId) clearTimeout(timeoutId);
                resolve({ value: true });
              } else if (response.data === 0) {
                // 尚未下载，开始定时下载请求
                count++;
                if (count === 5) {
                  if (intervalTime === 1000) {
                    intervalTime = 3000;
                  } else {
                    intervalTime = 5000;
                  }
                  count = 0;
                  clearTimeout(timeoutId);
                }
                timeoutId = setTimeout(fetchDataAndCheckStatus, intervalTime);
              } else {
                // 下载失败
                count = 0;
                if (timeoutId) clearTimeout(timeoutId);
                reject({ value: response.data });
              }
            } else {
              clearTimeout(timeoutId);
              timeoutId = setTimeout(fetchDataAndCheckStatus, intervalTime);
              reject({ value: response.success });
            }
          },
        });
      };
      fetchDataAndCheckStatus();
    });

  // 清空定时器的方法
  const resetTimeoutFunc = () => {
    count = 0;
    intervalTime = 1000;
    if (timeoutId) clearTimeout(timeoutId);
  };
  return {
    requestCheckFunc,
    resetTimeoutFunc,
  };
}
