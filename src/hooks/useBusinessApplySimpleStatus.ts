// 未开通业务 = 0
// 未认证 = 1
// 已认证 = 2
// 待审核 = 3
// 审核失败 = 4
// 待签约 = 5
// 完成 = 6
enum BusinessApplyStatus {
  'NOT_OPEN' = 0,
  'NOT_CERTIFIED' = 1,
  'CERTIFIED' = 2,
  'PENDING_REVIEW' = 3,
  'AUDIT_FAILURE' = 4,
  'TO_BE_SIGNED' = 5,
  'COMPLETION' = 6,
}
/**
 * @description: 判断是否开通业务
 * @MethodAuthor: dangh
 * @Date: 2023-07-10 15:18:35
 * @param {any} dispatch
 * @param {any} type
 */
export default function useBusinessApplySimpleStatus(dispatch: any, type: any) {
  return new Promise((resolve, reject) => {
    dispatch({
      type: 'homePage/getBusinessApplyStatus',
      payload: { type },
      callback: response => {
        if (response.success) {
          const data = response.data.applyStatus;
          if (data === BusinessApplyStatus['COMPLETION']) {
            resolve({ value: true });
          } else {
            reject({ value: false });
          }
        } else {
          reject({ value: response.success });
        }
      },
    });
  });
}
