// 未开通业务 = 0
// 未认证 = 1
// 已认证 = 2
// 待审核 = 3
// 审核失败 = 4
// 待签约 = 5

import { message } from 'antd';

// 完成 = 6
enum BusinessApplyStatus {
  'NOT_OPEN' = 0,
  'NOT_CERTIFIED' = 1,
  'CERTIFIED' = 2,
  'PENDING_REVIEW' = 3,
  'AUDIT_FAILURE' = 4,
  'TO_BE_SIGNED' = 5,
  'COMPLETION' = 6,
}
/*
 *@Description: 判断是否开通多个业务
 *@MethodAuthor: dangh
 *@Date: 2023-07-10 15:18:35
 */
export default function useBusinessApplyStatus(dispatch: any) {
  return new Promise((resolve, reject) => {
    dispatch({
      type: 'homePage/getBusinessStatusByTypes',
      payload: { types: [0, 1] },
      callback: response => {
        if (response.success) {
          const data = response.data;
          if (
            data[0] === BusinessApplyStatus['COMPLETION'] ||
            data[1] === BusinessApplyStatus['COMPLETION']
          ) {
            resolve({ value: true });
          } else {
            message.error('您尚未开通任何业务线，请开通成功后请再进行此操作！');
            reject({ value: false });
          }
        } else {
          reject({ value: response.success });
        }
      },
    });
  });
}
