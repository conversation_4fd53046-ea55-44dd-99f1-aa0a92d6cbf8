import { router } from 'umi';

/**
 * @description 主动发起补齐资料
 * @param dispatch 请求的dispatch
 */
export default function useCompleteData(dispatch: any, source: any = undefined) {
  return new Promise((resolve, reject) => {
    dispatch({
      type: 'realNameAuth/completeData',
      callback: response => {
        if (response.success) {
          resolve(response);
          if (source != undefined && source == 1) {
            const urlParams = new URL(window.location.href); // url
            const redirect = urlParams.origin + '/customerManager/customerInformation/realName';
            window.open(redirect);
          } else {
            router.push('/customerManager/customerInformation/realName');
          }
        } else {
          reject(response);
        }
      },
    });
  });
}
