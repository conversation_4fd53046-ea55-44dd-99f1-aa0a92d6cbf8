import { router } from 'umi';

/**
 * @description 发起补签合同
 * @param dispatch 请求的dispatch
 */
export default function useContractSign(dispatch: any, source: any = undefined) {
  return new Promise((resolve, reject) => {
    dispatch({
      type: 'smallBag/contractSign',
      callback: response => {
        if (response.success) {
          resolve(response);
          if (source != undefined && source == 1) {
            const urlParams = new URL(window.location.href); // url
            const redirect = urlParams.origin + '/homePageList';
            window.open(redirect);
          } else {
            router.push('/homePageList');
          }
        } else {
          reject(response);
        }
      },
    });
  });
}
