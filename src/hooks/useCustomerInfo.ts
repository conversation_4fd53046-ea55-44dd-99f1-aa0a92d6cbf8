/**
 * @description 获取实名认证后的信息
 * @param dispatch 请求的dispatch
 * @param param id和类型
 */
export default function useCustomerInfo(dispatch: any) {
  return new Promise<Record<string, unknown>>((resolve, reject) => {
    dispatch({
      type: 'realNameAuth/getCustomerAuthInfo',
      callback: response => {
        if (response.success) {
          resolve(response.data);
        } else {
          reject();
        }
      },
    });
  });
}
