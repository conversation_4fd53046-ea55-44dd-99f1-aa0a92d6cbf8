import { businessStatus } from '@/utils/commonConstant';
import { isAuth } from '@/utils/utils';

/**
 * @Description: 获取不同类型的业务线开通情况
 * @MethodAuthor: dangh
 * @Date: 2023-10-27 09:46:50
 */
export default function useOpenBusinessStatus(dispatch: any, type: any) {
  return new Promise<{ type: any; status: boolean }>((resolve, reject) => {
    const result = isAuth(type === 0 ? 'smallPackage' : 'fbaOrder');
    resolve({ type, status: result });
    // dispatch({
    //   type: 'user/getAuthorityStatus',
    //   payload: { type },
    //   callback: response => {
    //     if (response.success) {
    //       const key = `${response.data}`;
    //       if (businessStatus.includes(key)) {
    //         // status 为true 时表示已开通
    //         resolve({ type, status: false });
    //       } else {
    //         resolve({ type, status: true });
    //       }
    //     } else {
    //       reject();
    //     }
    //   },
    // });
  });
}
