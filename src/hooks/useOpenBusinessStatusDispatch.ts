import { businessStatus } from '@/utils/commonConstant';

/**
 * @Description: 获取不同类型的业务线开通情况
 * @MethodAuthor: dangh
 * @Date: 2023-10-27 09:46:50
 */
export default function useOpenBusinessStatusDispatch(dispatch: any, type: any) {
  return new Promise<{ type: any; status: boolean; key: string }>((resolve, reject) => {
    dispatch({
      type: 'user/getAuthorityStatus',
      payload: { type },
      callback: response => {
        if (response.success) {
          const key = `${response.data}`;
          if (businessStatus.includes(key)) {
            // status 为true 时表示已开通
            resolve({ type, status: false, key });
          } else {
            resolve({ type, status: true, key });
          }
        } else {
          reject();
        }
      },
    });
  });
}
