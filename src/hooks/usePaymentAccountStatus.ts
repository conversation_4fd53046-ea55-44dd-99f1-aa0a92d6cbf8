import useAuthStatus from './useAuthStatus';

export default function usePaymentAccountStatus(dispatch: any, businessType: any, param: any) {
  return new Promise((resolve, reject) => {
    dispatch({
      type: 'paymentAccount/getPaymentAccountStatus',
      payload: { businessType },
      callback: async response => {
        if (response.success) {
          if (response?.data) {
            useAuthStatus(dispatch, {
              contractType: 0,
              id: response?.data,
              ...param,
            });
          }
        } else {
          reject();
        }
      },
    });
  });
}
