/**
 * @description 单点登录跳转老系统
 * @param dispatch any
 */
export default function useSingleSignOn(dispatch: any) {
  dispatch({
    type: 'user/singleSignOn',
    callback: response => {
      if (response.success) {
        dispatch({
          type: 'login/logout',
          callback: result => {
            if (result.success) {
              window.location.replace(response.data);
            }
          },
        });
        // window.history.pushState(null, null, response.data);
        // history.pushState(null, null, '/new-page');
        // location.replace('/new-page');
        // window.location.assign(response.data);
        // window.location.href = response.data;
      }
    },
  });
}
