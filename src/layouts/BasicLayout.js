import React, { Suspense, Fragment } from 'react';
import { Layout, Card, Button, Modal, Row, Popconfirm } from 'antd';
import DocumentTitle from 'react-document-title';
import { connect } from 'dva';
import { ContainerQuery } from 'react-container-query';
import classNames from 'classnames';
import Media from 'react-media';
import logo from '../assets/logo.png';
import GlobalFooter from '@/components/GlobalFooter';
import RightContent from '@/components/BasicHeader/RightContent';
import PageLoading from '@/components/PageLoading';
import getPageTitle from '@/utils/getPageTitle';
import styles from './BasicLayout.less';
import { ProLayout, DefaultFooter } from '@ant-design/pro-components';
import { Link, router } from 'umi';
import { pageRouters, businessStatus, businessIntroduction } from '@/utils/commonConstant';
import { CopyrightOutlined, WechatOutlined, CloseOutlined } from '@ant-design/icons';
import Exception from '@/components/Exception';
import { pathIsNeedVerify, isRouteAuth, isAuth, handleBusinessStatus } from '@/utils/utils';
import IconFont from '@/components/IconFont';
import NoticeBg from '@/assets/notice_bg.png';
import NoticeBgLeft from '@/assets/notice_box_left.png';
import NoticeBoxRight from '@/assets/notice_box_right.png';
import NoticeTit from '@/assets/notice_tit.png';
import usePaymentAccountStatus from '@/hooks/usePaymentAccountStatus';
import CustomerFloatButton from '@/components/CustomerFloatButton';
import '@ant-design/flowchart/dist/index.css';
import Tour from 'reactour';
import { StyleProvider, legacyLogicalPropertiesTransformer } from '@ant-design/cssinjs';
import { formatMessage } from 'umi-plugin-react/locale';
import { initializeUdeskChat } from '@/utils/robot';
import { logSave, LogType } from '@/utils/logSave';

const { Content, Footer } = Layout;

const query = {
  'screen-xs': {
    maxWidth: 575,
  },
  'screen-sm': {
    minWidth: 576,
    maxWidth: 767,
  },
  'screen-md': {
    minWidth: 768,
    maxWidth: 991,
  },
  'screen-lg': {
    minWidth: 992,
    maxWidth: 1199,
  },
  'screen-xl': {
    minWidth: 1200,
    maxWidth: 1599,
  },
  'screen-xxl': {
    minWidth: 1600,
  },
};

const setTourContent = {
  小包专线: { title: '点击进行订单管理、账号管理、异常处理、地址维护', value: '1' },
  FBA专线: { title: '点击进行订单管理、地址维护', value: '2' },
  财务管理: { title: '点击查价格、收款账号、充值记录、对账等', value: '3' },
  用户管理: { title: '点击查注册/合同信息、修改密码、添加子用户', value: '4' },
};

const setTourNameOfValue = {
  小包专线: '1-step',
  FBA专线: '2-step',
  财务管理: '3-step',
  用户管理: '4-step',
};

class BasicLayout extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      lastPathName: '',
      dateY: '',
      smallBagStatus: false,
      fbaStatus: false,
      overseasStatus: false,
      noticeShow: false,
      noticeInfo: undefined,
      steps: [],
    };
  }

  componentDidMount() {
    this.getIMParam();
    const {
      dispatch,
      route: { routes, path, authority },
    } = this.props;
    dispatch({
      type: 'setting/getSetting',
    });
    dispatch({
      type: 'user/doYouNeedToShow',
      payload: { type: 'menu' },
    });
    dispatch({
      type: 'user/queryCurrent',
    });
    dispatch({
      type: 'user/MenuSearch',
    });
    dispatch({
      type: 'menu/getMenuByRole',
      callback: certificationMenuData => {
        let steps = [];
        Object.keys(setTourContent).forEach(value => {
          steps.push({
            selector: `[data-tut="${setTourContent[value].value}-step"]`,
            content: () => (
              <>
                <div
                  style={{
                    background: 'rgba(255, 255, 255, 0.9)',
                    borderRadius: '5px',
                    color: '#000',
                  }}
                >
                  <Row justify="space-between">
                    <h3 style={{ fontSize: '14px', color: '#8fc376', fontWeight: 900 }}>{value}</h3>{' '}
                    <Popconfirm
                      zIndex={9999999}
                      placement="top"
                      title={'是否跳过操作指引'}
                      onConfirm={this.closeTourSubmit}
                      okText="是"
                      cancelText="否"
                    >
                      <a style={{ color: 'rgb(232 226 226)' }}>
                        <CloseOutlined />
                      </a>
                    </Popconfirm>
                  </Row>
                  <p style={{ fontSize: '12px', color: '#3f3f3f' }}>
                    {setTourContent[value].title}
                  </p>
                </div>
              </>
            ),
            position: 'bottom',
            style: {
              boxShadow: 'none',
              width: '285px',
              textAlign: 'left',
              borderRadius: '8px',
              padding: '12px',
            },
          });
        });
        this.setState(
          {
            steps,
            smallBagStatus: isAuth('smallPackage'),
            fbaStatus: isAuth('fbaOrder'),
            overseasStatus: isAuth('overseas'),
          },
          () => {
            if (this.state.smallBagStatus && !this.props.isTourOpen) {
              this.getNotification();
            } else {
              this.setState({
                noticeShow: false,
              });
            }
          }
        );
        dispatch({
          type: 'menu/getMenuData',
          payload: { routes, path, authority, certificationMenuData },
        });
      },
    });
    this.getNowYearTime();
  }

  getIMParam = () => {
    const { dispatch } = this.props;
    dispatch({
      type: 'homePage/getIMParam',
      callback: result => {
        if (result.success) {
          let customer = result.data;
          initializeUdeskChat('56688', customer);
        }
      },
    });
  };

  getNowYearTime = () => {
    const { dispatch } = this.props;
    dispatch({
      type: 'menu/getNowYear',
      callback: res => {
        if (res.success) {
          this.setState({
            dateY: res.data,
          });
        } else {
          this.setState({
            dateY: null,
          });
        }
      },
    });
  };

  getNotification = () => {
    const { dispatch } = this.props;
    dispatch({
      type: 'user/getNotification',
      callback: response => {
        if (response.success) {
          this.setState({
            noticeShow: response?.data ? true : false,
            noticeInfo: response?.data
              ? {
                  ...response?.data,
                }
              : undefined,
          });
        } else {
          this.setState({
            noticeShow: false,
            noticeInfo: undefined,
          });
        }
      },
    });
  };

  getContext() {
    const { location, breadcrumbNameMap } = this.props;
    return {
      location,
      breadcrumbNameMap,
    };
  }

  handleMenuCollapse = collapsed => {
    const { dispatch } = this.props;
    dispatch({
      type: 'global/changeLayoutCollapsed',
      payload: collapsed,
    });
  };

  /**
   *
   * @param {string} pathname 路由地址
   */
  getCurrentUserStatus = pathname => {
    const { dispatch } = this.props;
    const type = pathname.includes('smallBag')
      ? 0
      : pathname.includes('fba')
      ? 1
      : pathname.includes('overseas')
      ? 2
      : 0;
    dispatch({
      type: 'user/getAuthorityStatus',
      payload: { type },
      callback: response => {
        if (response.success) {
          const key = `${response.data}`;
          if (pageRouters[key]) {
            // 如果时客服发起的付款委托书是否需要强制跳转
            if (key == 9 || key == 10) {
              router.push(pageRouters[key]);
            }
            if (pathIsNeedVerify(pathname)) {
              sessionStorage.setItem(
                'USER_ACTION_BUSINESS',
                JSON.stringify({ smallBag: type === 0, fba: type === 1, overseas: type === 2 })
              );
              router.push(pageRouters[key]);
            } else {
              sessionStorage.setItem(
                'USER_ACTION_BUSINESS',
                JSON.stringify({ smallBag: false, fba: false, overseas: false })
              );
            }
          } else {
            sessionStorage.setItem(
              'USER_ACTION_BUSINESS',
              JSON.stringify({ smallBag: false, fba: false, overseas: false })
            );
          }
        }
      },
    });
  };

  // 判断是否是轨迹查询菜单
  isTrackMenu = location => {
    if (location.pathname === '/fba/trackQuery') {
      // https://track.yw56.com.cn       生产
      // http://10.10.144.16       测试
      let url = 'http://10.10.144.16';
      const { dispatch } = this.props;
      dispatch({
        type: 'fba/encodeFbaMerchantCode',
        callback: response => {
          if (response.success) {
            const params = `${url}?target=${response.data.target}&time=${response.data.time}`;
            window.open(params);
            router.push('/fba/specialLine');
          } else {
            router.push('/fba/specialLine');
          }
        },
      });
    }
  };

  handleIncludeRoute = location => {
    const { menuData } = this.props;

    if (!isRouteAuth(location.pathname)) {
      // 需要特殊处理的path
      const parentPath = location.pathname.split('/')[1];
      const data = menuData.find(item => item.path == `/${parentPath}`)?.routes ?? [];
      if (data.length === 0) return router.push('/exception/403');
      const noHideRoutes = data
        .filter(item => !item.hideInMenu)
        .reduce((acc, current) => {
          if (current.routes) {
            acc.push(...current.routes);
          }
          acc.push(current);
          return acc;
        }, []);
      if (noHideRoutes.length > 0) {
        router.push(noHideRoutes[0].path);
      } else {
        router.push('/exception/403');
      }
    }
  };

  handlePageChange = location => {
    const { dispatch, menuData } = this.props;
    const { lastPathName } = this.state;

    if (location.pathname === lastPathName) {
      return;
    }
    if (location.pathname !== '/index' || location.pathname !== '/homePageList') {
      this.getCurrentUserStatus(location.pathname);
    }
    this.isTrackMenu(location);
    this.handleIncludeRoute(location);
    this.setState({
      lastPathName: location.pathname,
    });
  };

  // 处理之前点击路由后的处理逻辑
  makePathName = async itemPath => {
    const PATHNAME = sessionStorage.getItem('PATHNAME');
    const miscellaneous = JSON.parse(localStorage.getItem('miscellaneous'));
    window.scroll(0, 0);
    if (
      itemPath === '/smallBag/orderManagement/orderList' &&
      PATHNAME &&
      PATHNAME !== '/smallBag/orderManagement/orderList' &&
      miscellaneous
    ) {
      localStorage.setItem(
        'miscellaneous',
        JSON.stringify({
          ...miscellaneous,
          isRefresh: true,
          tabNumber: '9',
          isClickSubmit: false,
        })
      );
    } else if (itemPath === '/smallBag/orderManagement/orderList') {
      localStorage.removeItem('miscellaneous');
    }

    sessionStorage.setItem('PATHNAME', itemPath);

    try {
      const businessType = itemPath.includes('smallBag')
        ? 0
        : itemPath.includes('fba')
        ? 1
        : itemPath.includes('overseas')
        ? 2
        : undefined;
      if (businessType !== undefined) {
        await usePaymentAccountStatus(this.props.dispatch, businessType, { viewContract: true });
      }
    } catch (error) {
      console.error(error);
    }
  };

  // 处理渲染菜单
  handleMenuItemRender = (item, dom) => {
    const { name } = item;
    const { dispatch } = this.props;
    const { smallBagStatus, fbaStatus, overseasStatus } = this.state;
    if ((item.key === '/smallBag' || item.key.includes('smallBag')) && !smallBagStatus) {
      return (
        <div
          data-tut={setTourNameOfValue[name]}
          onClick={event => handleBusinessStatus('0', dispatch)}
        >
          {dom}
        </div>
      );
    } else if ((item.key === '/fba' || item.key.includes('fba')) && !fbaStatus) {
      return (
        <div
          data-tut={setTourNameOfValue[name]}
          onClick={event => handleBusinessStatus('1', dispatch)}
        >
          {dom}
        </div>
      );
    } else if ((item.key === '/overseas' || item.key.includes('overseas')) && !overseasStatus) {
      //
      return (
        <div
          data-tut={setTourNameOfValue[name]}
          onClick={event => handleBusinessStatus('2', dispatch)}
        >
          {dom}
        </div>
      );
    } else {
      return (
        <Link
          data-tut={setTourNameOfValue[name]}
          to={item.path}
          onClick={() => this.makePathName(item.path)}
        >
          {dom}
        </Link>
      );
    }
  };

  // 关闭通知时处理逻辑
  handleNoticeClose = () => {
    const { dispatch } = this.props;
    const { noticeInfo } = this.state;
    dispatch({
      type: 'homePage/journalism',
      payload: {
        contentId: noticeInfo?.contentId,
      },
      callback: response => {
        if (response.success) {
          this.setState({
            noticeShow: false,
          });
        }
      },
    });
  };

  // 处理渲染通知
  handleNoticeRender = () => {
    const { noticeShow, noticeInfo } = this.state;
    return noticeShow ? (
      <div className="w-full p-1" style={{ height: '86px' }}>
        {/* notice-box-top */}
        <div
          className="relative block h-1"
          style={{
            backgroundSize: '44px 3px',
            background: `#ffffff url(${NoticeBg}) -7px bottom repeat-x`,
          }}
        />
        {/* notice-box-content */}
        <div
          className="relative block bg-white"
          style={{
            height: '70px',
          }}
        >
          {/* notice-box-pre */}
          <div
            className="absolute block w-1 "
            style={{
              height: '70px',
              background: `url(${NoticeBgLeft}) repeat-y`,
              backgroundSize: '3px 44px',
            }}
          />
          {/*notice-box-con */}
          <div
            className="inline overflow-ellipsis overflow-hidden"
            style={{
              height: '60px',
              padding: '12px 15px',
              marginRight: '330px',
              display: '-webkit-box',
              WebkitLineClamp: 2,
              lineClamp: 2,
              WebkitBoxOrient: 'vertical',
            }}
          >
            <p style={{ textIndent: '15px', lineHeight: '24px', marginBottom: '5px' }}>
              {noticeInfo?.title ?? ''}
            </p>
            <a
              onClick={() => {
                logSave(LogType.home2);
                // 进入详情
                router.push({
                  pathname: '/serviceManagement/content/notificationMessage',
                  state: { contentId: noticeInfo?.contentId, conList: [], param: {} },
                });
              }}
              className="block bg-white text-center top-0 absolute"
              style={{ right: '295px', width: '50px', height: '70px' }}
            >
              详情
            </a>
          </div>
        </div>
        {/* notice-box-next */}
        <div
          className="absolute block right-0 top-1 "
          style={{
            background: `url(${NoticeBoxRight}) repeat-y`,
            backgroundSize: '3px 44px',
            width: '3px',
            height: '70px',
          }}
        />
        {/* notice-box-tit */}
        <div
          className="absolute block top-0 right-0"
          style={{
            height: '80px',
            width: '300px',
            background: `url(${NoticeTit}) no-repeat top right`,
            backgroundSize: '100%',
          }}
        >
          <span
            style={{ float: 'right', marginRight: '3px', marginTop: '3px' }}
            onClick={this.handleNoticeClose}
          >
            <div
              style={{
                borderRadius: '100px',
                border: '1px solid rgb(136, 136, 136)',
                width: '15px',
                height: '15px',
                lineHeight: '15px',
                textAlign: 'center',
                color: 'rgb(136, 136, 136)',
                fontSize: '10px',
                cursor: 'pointer',
              }}
            >
              X
            </div>
          </span>
        </div>
        {/* notice-box-bottom */}
        <div
          className="relative block h-1"
          style={{
            background: `#ffffff url(${NoticeBg}) -7px bottom repeat-x`,
            backgroundSize: '44px 3px',
          }}
        />
      </div>
    ) : null;
  };

  // 添加展示客服信息
  handleCustomerService = () => {
    return <CustomerFloatButton {...this.props} />;
  };

  closeTour = () => {
    this.closeTourSubmit();
  };

  closeTourSubmit = () => {
    const { dispatch } = this.props;
    const { smallBagStatus } = this.state;
    dispatch({
      type: 'user/viewTheEnd',
      payload: { type: 'menu' },
      callback: response => {
        if (response.success) {
          if (smallBagStatus) {
            // 只有开通小包业务线后才能请求公告
            this.getNotification();
          }
        }
      },
    });
  };

  changeModifyName = arr => {
    arr.forEach(item => {
      // 检查对象是否有name属性，如果有，则进行修改
      if (item.hasOwnProperty('name')) {
        item.name = formatMessage({ id: item.name }); // 修改name属性值
      }

      // 如果对象有routes数组，遍历每个元素并递归调用modifyNames
      if (Array.isArray(item.routes)) {
        item.routes.forEach(route => this.changeModifyName([route])); // 递归调用
      }
    });
  };

  render() {
    const {
      navTheme,
      children,
      location: { pathname },
      isMobile,
      menuData,
      breadcrumbNameMap,
      fixedHeader,
      menuLoading,
      setting,
      pageError,
      isTourOpen,
      user,
    } = this.props;
    const { dateY, noticeShow, smallBagStatus, fbaStatus, overseasStatus, steps } = this.state;

    const settings = {
      ...setting,
    };
    const contentStyle = !fixedHeader ? { paddingTop: 0 } : {};
    const dateYu = new Date().getFullYear();
    const renderTour = (
      <Tour
        steps={steps}
        isOpen={isTourOpen}
        onRequestClose={this.closeTour}
        closeWithMask={false}
        maskClassName={styles.mask}
        nextButton={
          <div
            style={{
              padding: '6px 14px',
              borderRadius: '4px',
              color: 'rgb(245, 245, 245)',
              backgroundColor: '#52c41a',
            }}
          >
            下一步
          </div>
        }
        prevButton={
          <div
            style={{
              padding: '6px 14px',
              borderRadius: '4px',
              color: 'rgb(204, 204, 204)',
              border: '1px solid rgb(237 237 237)',
            }}
          >
            上一步
          </div>
        }
        lastStepNextButton={
          <div
            style={{
              padding: '6px 14px',
              borderRadius: '4px',
              color: 'rgb(245, 245, 245)',
              backgroundColor: '#52c41a',
            }}
          >
            知道了
          </div>
        }
        rounded={4}
        maskSpace={4}
        showCloseButton={false}
        showNavigationNumber={false}
        showNavigation={false}
        showNumber={false}
        disableKeyboardNavigation={['esc']}
        disableInteraction={true}
      />
    );
    const layout = (
      <ProLayout
        logo={logo}
        className={`pro-layout-customize ${noticeShow ? 'pro-layout-changeHeader' : ''}`}
        style={{ minHeight: '100vh' }}
        disableMobile={true}
        contentStyle={{ minHeight: '84vh' }}
        token={{
          colorPrimary: '#7cb830',
          header: {
            colorHeaderTitle: '#7cb830',
            colorTextMenuSelected: '#7cb830',
          },
          sider: {
            colorTextMenuSelected: '#7cb830',
            colorMenuBackground: '#ffffff',
          },
          pageContainer: {
            paddingInlinePageContainerContent: 20,
            // paddingBlockPageContainerContent: 20,
          },
        }}
        onCollapse={this.handleMenuCollapse}
        onMenuHeaderClick={() => window.open('https://www.yw56.com.cn/')}
        onPageChange={location => {
          this.handlePageChange(location);
        }}
        menuDataRender={() => {
          const parentPathIndex = menuData?.findIndex(item => item.path === '/financialManagement');
          const childPathIndex = menuData?.[parentPathIndex]?.routes?.findIndex(
            item => item.path === '/financialManagement/rechargeManagement'
          );
          if (parentPathIndex !== -1 && childPathIndex !== -1) {
            const hideInMenu = !(smallBagStatus || fbaStatus || overseasStatus);
            menuData[parentPathIndex].routes[childPathIndex].hideInMenu = hideInMenu;
          }
          this.changeModifyName(menuData);
          return menuData;
        }}
        menuItemRender={(item, dom) => this.handleMenuItemRender(item, dom)}
        footerRender={() => (
          <Footer style={{ background: '#ffffff' }}>
            <GlobalFooter
              copyright={
                <Fragment>
                  <div style={{ color: '#69789E' }}>
                    Copyright <CopyrightOutlined /> {dateY != null ? dateY : dateYu} 燕文物流
                  </div>
                </Fragment>
              }
            />
          </Footer>
        )}
        headerRender={(props, defaultDom) => (
          <div>
            {/* <div
              style={{ height: '86px' }}
              onClick={() => this.setState({ noticeShow: !noticeShow })}
            >
              测试重要通告
            </div> */}
            {this.handleNoticeRender()}
            {defaultDom}
          </div>
        )}
        rightContentRender={() => (
          <RightContent smallBagStatus={smallBagStatus} fbaStatus={fbaStatus} {...this.props} />
        )}
        {...settings}
        headerTitleRender={(logoDom, titleDom) => {
          return (
            <div className="flex items-center" style={{ marginRight: '40px' }}>
              {logoDom}
              <h1>{formatMessage({ id: '客户服务中心' })}</h1>
            </div>
          );
        }}
      >
        {/* {this.handleCustomerService()} */}
        {children}
        {renderTour}
      </ProLayout>
    );

    return (
      <StyleProvider hashPriority="high" transformers={[legacyLogicalPropertiesTransformer]}>
        <React.Fragment>
          {menuLoading ? (
            <PageLoading />
          ) : (
            <DocumentTitle title={getPageTitle(pathname, breadcrumbNameMap)}>
              <ContainerQuery query={query}>
                {params => <div className={classNames(params)}>{layout}</div>}
              </ContainerQuery>
            </DocumentTitle>
          )}
        </React.Fragment>
      </StyleProvider>
    );
  }
}

export default connect(
  ({ global, setting, menu: menuModel, messageCenter, homePage, user, loading }) => ({
    setting,
    user,
    sendReadMessage: messageCenter?.sendReadMessage,
    downloadMessage: homePage?.downloadMessage,
    isTourOpen: user.isTourOpen,
    collapsed: global.collapsed,
    pageError: global.error,
    menuData: menuModel.menuData,
    breadcrumbNameMap: menuModel.breadcrumbNameMap,
    // menuLoading: loading.effects['menu/getCertificationMenuData'],
    menuLoading: loading.effects['menu/getMenuByRole'],
  })
)(props => (
  <Media query="(max-width: 599px)">
    {isMobile => <BasicLayout {...props} isMobile={isMobile} />}
  </Media>
));
