@import '~antd/lib/style/themes/default.less';

.content {
  margin: 24px;
  padding-top: 10px;
}

.notiContent {
  margin: 24px;
  padding-top: @layout-header-height + 86px;
}

.mask {
  opacity: 0.5;
}

.tourModal {
  .bqHzuc {
    margin-top: 10px;
  }
}

// :global {

// }

:global {
  .ant-radio-button-part {
    .ant-radio-button-wrapper {
      margin-right: 25px;
      margin-bottom: 10px;
      border-radius: 4px;
      border: 1px solid #d9d9d9;
      padding-right: 8px;
      padding-left: 8px;
      height: 22px;
      line-height: 22px;
    }

    .ant-radio-button-wrapper:not(:first-child)::before {
      width: 0;
    }
  }


  .ant-radio-button-part-no-border {
    .ant-radio-button-wrapper {
      margin-right: 25px;
      margin-bottom: 10px;
      border-radius: 4px;
      border: none;
      padding-right: 8px;
      padding-left: 8px;
      height: 22px;
      line-height: 22px;
    }

    .ant-radio-button-wrapper:not(:first-child)::before {
      width: 0;
    }
    .ant-radio-button-wrapper:first-child {
      border: none;
    }
  }

  .ant-radio-button-split {
    .ant-radio-button-wrapper {
      margin-right: 8px;
      border: none;
      border-left: none !important;
      border-radius: 3px;
      padding: 1px 10px;
    }

    .ant-radio-button-wrapper:not(:first-child)::before {
      width: 0;
    }
  }

  .ant-flex-form .ant-form-item {
    display: flex;
  }

  .ant-flex-form .ant-form-item-control-wrapper {
    flex: 1;
  }

  .ant-pro-layout-container .ant-pro-layout-content {
    padding-block: 0 !important;
    padding-inline: 0 !important;
  }

  .ant-form-item-no-content {
    .ant-form-item-label {
      label.ant-form-item-required::before {
        content: none;
      }
    }
  }

  .ant-portal-radio-button {
    .ant-radio-button-wrapper {
      margin-right: 40px;
      border-radius: 6px;
      border: 1px solid #d9d9d9;
    }

    .ant-radio-button-wrapper:not(:first-child)::before {
      background-color: transparent;
    }

    .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {
      color: #fff;
      background: #52c41a;
      border-color: #52c41a;
    }

    .ant-form-item {
      margin-bottom: 15px;
    }
  }

  .ant-portal-no-form-bottom {
    .ant-form-item {
      margin-bottom: 0;
    }
  }

  .pro-layout-customize {
    .ant-pro-top-nav-header-menu {
      padding-block: 0px !important;
    }
    .ant-pro-top-nav-header-base-menu {
      height: 100% !important;
      .ant-menu-overflow-item {
        padding: 6.5px 20px !important;
      }
    }
  }
  .pro-layout-changeHeader {
    .ant-layout-header {
      height: 142px !important;
    }
    .ant-pro-sider-mix {
      height: calc(100% - 142px) !important;
      top: 142px !important;
    }
  }
  .pro-layout-backHeader {
    .ant-layout-header {
      height: 56px !important;
    }
    .ant-pro-sider-mix {
      height: calc(100% - 56px) !important;
      top: 56px !important;
    }
  }

  .editable-row {
    .ant-row {
      width: 100%;
    }
  }
  
  //
}
