import React, { Component, Fragment } from 'react';
import { formatMessage } from 'umi/locale';
import { connect } from 'dva';
import Link from 'umi/link';
import { CopyrightOutlined } from '@ant-design/icons';
import DocumentTitle from 'react-document-title';
import GlobalFooter from '@/components/GlobalFooter';
// import SelectLang from '@/components/SelectLang';
import styles from './UserLayout.less';
import logo from '../assets/logo.png';
import getPageTitle from '@/utils/getPageTitle';

const links = [
  {
    key: 'help',
    title: formatMessage({ id: 'layout.user.link.help' }),
    href: '',
  },
  {
    key: 'privacy',
    title: formatMessage({ id: 'layout.user.link.privacy' }),
    href: '',
  },
  {
    key: 'terms',
    title: formatMessage({ id: 'layout.user.link.terms' }),
    href: '',
  },
];
const dateYu = new Date().getFullYear();
class UserLayout extends Component {
  constructor(props) {
    super(props);
    this.state = {
      dateY: '',
    };
  }
  componentDidMount() {
    const {
      dispatch,
      route: { routes, authority },
    } = this.props;
    dispatch({
      type: 'menu/getMenuData',
      payload: { routes, authority },
    });
    this.getNowYearTime();
    // dispatch({
    //   type: 'menu/nowYearTime',
    //   callback: res => {
    //     if (res.result) {
    //       this.setState({
    //         dateY: res.data,
    //       });
    //     } else {
    //       this.setState({
    //         dateY: null,
    //       });
    //     }
    //   },
    // });
  }

  getNowYearTime = () => {
    const { dispatch } = this.props;
    dispatch({
      type: 'menu/getNowYear',
      callback: res => {
        if (res.success) {
          this.setState({
            dateY: res.data,
          });
        } else {
          this.setState({
            dateY: null,
          });
        }
      },
    });
  };

  render() {
    const {
      children,
      location: { pathname },
      breadcrumbNameMap,
    } = this.props;
    const { dateY } = this.state;
    const copyright = (
      <Fragment>
        Copyright <CopyrightOutlined /> {dateY != null ? dateY : dateYu} 燕文物流
      </Fragment>
    );

    return (
      <DocumentTitle title={getPageTitle(pathname, breadcrumbNameMap)}>
        <div className={styles.container}>
          <div className={styles.lang}>
            <span
              style={{
                fontSize: 14,
                height: 40,
                paddingRight: 20,
                paddingTop: 20,
                textAlign: 'center',
              }}
            >
              <a href="http://www.yw56.com.cn/" style={{ color: 'black' }}></a>
            </span>
          </div>
          <div className={styles.content}>
            <div className={styles.top}>
              <div className={styles.header}>
                <img
                  alt="logo"
                  className={styles.logo}
                  src={logo}
                  onClick={() => window.open('https://www.yw56.com.cn/')}
                />
                {/* <Link to="/">
                </Link> */}
              </div>
              <div className={styles.desc}></div>
            </div>
            {children}
          </div>
          <GlobalFooter links={links} copyright={copyright} />
        </div>
      </DocumentTitle>
    );
  }
}

export default connect(({ menu: menuModel }) => ({
  menuData: menuModel.menuData,
  breadcrumbNameMap: menuModel.breadcrumbNameMap,
}))(UserLayout);
