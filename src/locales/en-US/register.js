export default {
  'app.register': 'register',
  'app.register.businessType': 'Cooperative business',
  'app.register.tickets': 'Average daily ticket volume',
  'app.register.email': 'email',
  'app.register.userName': 'user name',
  'app.register.mobile': 'cell-phone number',
  'app.register.smsCode': 'identifying code',
  'app.register.getSmsCode': 'Get verification code',
  'app.register.pwd': 'enter password',
  'app.register.confirmPwd': 'confirm password',
  'app.register.picCode': 'Graphic verification code',
  'app.register.sale': 'Recommended sales',
  'app.register.noSale': 'No recommended sales',
  'app.register.haveAccountLogin': 'Has an account? Login in immediately',
  'app.register.smallBusiness': 'Small line',
  'app.register.fbaBusiness': 'FBA special railway line',
  'app.register.city': 'In the city',
  'app.register.validation.selectBusiness': 'Please select a partner business',
  'app.register.validation.selectTickets': 'Please select the daily ticket volume',
  'app.register.validation.email': 'The email address is incorrect correctly',
  'app.register.validation.validateUserName':
    'The user name is 6 to 20 letters, numbers or underlined two of them',
  'app.register.validation.mobileWrong': 'The mobile phone number is incorrectly formed',
  'app.register.validation.pwdWrongTitle':
    'Please enter a password composed of 9 to 20 digits, letters or symbols, the symbols can have _.!@#?',
  'app.register.validation.pwdWrong':
    'The password consists of 9~20 digits, letters or symbols, and the symbols can have _.!@#?',
  'app.register.validation.confirmPwd': 'Please confirm the password',
  'app.register.validation.confirmPwdError': 'The password entered twice does not match!',
  'app.register.validation.province': 'Please select the province!',
  'app.register.validation.city': 'Please choose your home city!',
  'app.register.validation.agreement': 'Please read and agree to the agreement!',
  'app.register.placeholder.email': 'Please enter the mailbox',
  'app.register.placeholder.userName': 'Please enter the username',
  'app.register.placeholder.mobile': 'Please enter the mobile phone number',
  'app.register.placeholder.smsCode': 'Please enter the verification code',
  'app.register.placeholder.pwd': 'Please enter the password',
  'app.register.placeholder.confirmPwd': 'Enter your password again',
  'app.register.placeholder.picCode': 'Please enter the graphic verification code on the right',
  'app.register.placeholder.saleMobile': 'Please enter the sales phone number',
  'app.register.placeholder.read': 'I have read and agreed',
  'app.register.placeholder.ywAgreement': 'Yanwen logistics Service Agreement',
  'app.register.placeholder.personAgreement': 'Personal Privacy Agreement',
};
