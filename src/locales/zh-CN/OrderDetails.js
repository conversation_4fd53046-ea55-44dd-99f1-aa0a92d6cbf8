export default {
  箱单信息图片展示: '箱单信息图片展示',
  商户状态: '商户状态',
  国家: '国家',
  国家名称: '国家名称',
  制单账号: '制单账号',
  亚马逊: '亚马逊',
  燕文主单号: '燕文主单号',
  主单号: '主单号',
  箱单列表: '箱单列表',
  申报信息列表: '申报信息列表',
  收货人门牌号: '收货人门牌号',
  是否报关: '是否报关',
  是: '是',
  否: '否',
  是否含电: '是否含电',
  是否保险: '是否保险',
  是否包税: '是否包税',
  地址: '地址',
  收货人邮编: '收货人邮编',
  产品名字: '产品名字',
  收货人城市: '收货人城市',
  收货人公司: '收货人公司',
  收货人邮箱: '收货人邮箱',
  收货人名字: '收货人名字',
  收件人电话: '收件人电话',
  收货人省: '收货人省',
  州: '州',
  备注: '备注',
  预计送货日期: '预计送货日期',
  计费重: '计费重',
  总预估体积重: '总预估体积重',
  总数量: '总数量',
  总体积: '总体积',
  总重量: '总重量',
  仓库编码: '仓库编码',
  仓库类型: '仓库类型',
  送货方式: '送货方式',
  退回原因: '退回原因',
  城市: '城市',
  币种: '币种',
  客户单号: '客户单号',
  入库签: '入库签',
  入库单: '入库单',
  获取: '获取',
  箱单实际测量图片: '箱单实际测量图片',
  无图片: '无图片',
  获取账单信息: '获取账单信息',
  导出: '导出',
  箱单实际测量信息: '箱单实际测量信息',
  确认账单: '确认账单',
  下载账单: '下载账单',
  合计: '合计',
  总体积重: '总体积重',
  生成标签: '生成标签',
  入单签: '入单签',
  点击一键复制: '点击一键复制',
  仓: '仓',
  预报: '预报',
  测量: '测量',
  序号: '序号',
  费用名称: '费用名称',
  金额: '金额',
  箱单信息: '箱单信息',
  标签号: '标签号',
  箱号: '箱号',
  单箱长: '单箱长',
  单箱宽: '单箱宽',
  单箱高: '单箱高',
  单箱重量: '单箱重量',
  预计体积重: '预计体积重',
  操作: '操作',
  图片: '图片',
  申报信息: '申报信息',
  中文品名: '中文品名',
  英文品名: '英文品名',
  产品: '产品',
  申报单价: '申报单价',
  数量: '数量',
  个: '个',
  重量: '重量',
  海关编码: '海关编码',
  材质: '材质',
  型号: '型号',
  用途: '用途',
  品牌: '品牌',
  品牌类型: '品牌类型',
  净重: '净重',
  采购单价: '采购单价',
  产品图片链接: '产品图片链接',
  产品销售链接: '产品销售链接',
  一键复制: '一键复制',
  其他信息和制单账号: '其他信息和制单账号',
  基础信息: '基础信息',
  购买保险: '购买保险',
  产品名称: '产品名称',
  交税模式: '交税模式',
  包税: '包税',
  自税: '自税',
  递延: '递延',
  目的国: '目的国',
  预约信息: '预约信息',
  发货方式: '发货方式',
  客户自送: '客户自送',
  燕文揽收: '燕文揽收',
  仓库联系人: '仓库联系人',
  仓库联系电话: '仓库联系电话',
  收货人信息: '收货人信息',
  仓库: '仓库',
  亚马逊仓库: '亚马逊仓库',
  私人仓库: '私人仓库',
  跟踪单号: '跟踪单号',
  仓库代码: '仓库代码',
  姓名: '姓名',
  邮编: '邮编',
  号: '号',
  公司: '公司',
  州省: '州省',
  电话: '电话',
  邮箱: '邮箱',
  申报总价值: '申报总价值',
  其他信息: '其他信息',
  是否单独报关: '是否单独报关',
  预计自送时间: '预计自送时间',
  期望揽收时间: '期望揽收时间',
  司机姓名: '司机姓名',
  联系电话: '联系电话',
  预计上门揽收时间: '预计上门揽收时间',
  返回: '返回',
  费用明细: '费用明细',
  实重: '实重',
  体积重: '体积重',
  下载明细: '下载明细',
};
