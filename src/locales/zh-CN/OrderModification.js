export default {
  是否允许私人仓库: '是否允许私人仓库',
  是否允许: '是否允许',
  仓库: '仓库',
  是否允许揽收: '是否允许揽收',
  是否允许自送: '是否允许自送',
  揽收仓库限制: '揽收仓库限制',
  自送仓库限制: '自送仓库限制',
  国家: '国家',
  国家名称: '国家名称',
  制单账号: '制单账号',
  亚马逊: '亚马逊',
  运单: '运单',
  箱单列表: '箱单列表',
  申报信息列表: '申报信息列表',
  收货人门牌号: '收货人门牌号',
  是否报关: '是否报关',
  是: '是',
  否: '否',
  是否含电: '是否含电',
  是否保险: '是否保险',
  是否包税: '是否包税',
  地址: '地址',
  收货人邮编: '收货人邮编',
  产品名字: '产品名字',
  收货人城市: '收货人城市',
  收货人公司: '收货人公司',
  收货人邮箱: '收货人邮箱',
  收货人名字: '收货人名字',
  收件人电话: '收件人电话',
  收货人省: '收货人省',
  州: '州',
  备注: '备注',
  预计送货日期: '预计送货日期',
  计费重: '计费重',
  总预估体积重: '总预估体积重',
  总数量: '总数量',
  总体积: '总体积',
  总重量: '总重量',
  仓库编码: '仓库编码',
  仓库类型: '仓库类型',
  送货方式: '送货方式',
  主单号: '主单号',
  状态: '状态',
  审核失败原因: '审核失败原因',
  燕文仓编码或: '燕文仓编码或',
  币种: '币种',
  客户单号: '客户单号',
  获取制单账号: '获取制单账号',
  默认选择的制单账号: '默认选择的制单账号',
  目的国: '目的国',
  数据: '数据',
  显示: '显示',
  数据数组: '数据数组',
  收货人信息选择亚马逊还是私人: '收货人信息选择亚马逊还是私人',
  私人: '私人',
  产品名称下拉框值: '产品名称下拉框值',
  商户状态: '商户状态',
  揽收模式数据: '揽收模式数据',
  是否支持燕文揽收: '是否支持燕文揽收',
  是否显示支持购买保险: '是否显示支持购买保险',
  选中的申报信息: '选中的申报信息',
  初始化请求状态: '初始化请求状态',
  跳转: '跳转',
  请刷新: '请刷新',
  获取客户自送地址: '获取客户自送地址',
  过滤一层限制: '过滤一层限制',
  获取到原始数据: '获取到原始数据',
  没有选择的数据: '没有选择的数据',
  仓: '仓',
  获取目的国: '获取目的国',
  获取图片链接显示的参数: '获取图片链接显示的参数',
  获取产品名称: '获取产品名称',
  获取国家: '获取国家',
  获取缴税模式: '获取缴税模式',
  验证制单账号是否支持燕文揽收: '验证制单账号是否支持燕文揽收',
  获取发货方式: '获取发货方式',
  获取: '获取',
  详情: '详情',
  附件: '附件',
  获取产品: '获取产品',
  如果接口返回有产品编号: '如果接口返回有产品编号',
  请求缴税接口: '请求缴税接口',
  请求是否支持购买保险接口: '请求是否支持购买保险接口',
  根据: '根据',
  获取详情: '获取详情',
  点击草稿: '点击草稿',
  请选择制单账号: '请选择制单账号',
  请选择目的国: '请选择目的国',
  请选择产品名称: '请选择产品名称',
  私人仓库: '私人仓库',
  燕文仓库编码: '燕文仓库编码',
  草稿订单生成运单: '草稿订单生成运单',
  获取燕文揽收仓地址: '获取燕文揽收仓地址',
  修改不展示地址问题: '修改不展示地址问题',
  获取计泡系数: '获取计泡系数',
  根据国家: '根据国家',
  获取亚马逊揽收仓: '获取亚马逊揽收仓',
  选择收货人信息: '选择收货人信息',
  传递给子组件修改: '传递给子组件修改',
  选择产品名称: '选择产品名称',
  检测该产品是否支持购买保险: '检测该产品是否支持购买保险',
  获取运单配置: '获取运单配置',
  限制只允许私人仓库还是: '限制只允许私人仓库还是',
  限制: '限制',
  仓库只允许下那些仓库: '仓库只允许下那些仓库',
  限制只允许自送还是揽收: '限制只允许自送还是揽收',
  限制揽收仓的选择: '限制揽收仓的选择',
  限制自送仓的选择: '限制自送仓的选择',
  私人地址: '私人地址',
  限制只能揽收还是自送: '限制只能揽收还是自送',
  揽收: '揽收',
  自送: '自送',
  设置标题和描述: '设置标题和描述',
  刷新仓库: '刷新仓库',
  根据产品编号验证是否支持购买保险: '根据产品编号验证是否支持购买保险',
  选择: '选择',
  获取子组件: '获取子组件',
  批量上传箱单信息: '批量上传箱单信息',
  箱单信息: '箱单信息',
  申报信息: '申报信息',
  请上传小于: '请上传小于',
  的文件: '的文件',
  请按照模板文件上传: '请按照模板文件上传',
  文件不能为空: '文件不能为空',
  获取箱单表格内的数据: '获取箱单表格内的数据',
  获取申报表格内的数据: '获取申报表格内的数据',
  设置: '设置',
  计算总数: '计算总数',
  合计: '合计',
  总体积重: '总体积重',
  下载箱单信息和申报模板: '下载箱单信息和申报模板',
  点击按钮: '点击按钮',
  购买保险: '购买保险',
  客户自送: '客户自送',
  禁止选择的时间: '禁止选择的时间',
  图片上传: '图片上传',
  的图片: '的图片',
  请上传图片或者压缩包: '请上传图片或者压缩包',
  值: '值',
  删除图片: '删除图片',
  燕文揽收: '燕文揽收',
  提交: '提交',
  该产品箱单信息最大为: '该产品箱单信息最大为',
  您已超过最大限制: '您已超过最大限制',
  请减少箱单信息: '请减少箱单信息',
  判定是否有未填的信息: '判定是否有未填的信息',
  请完整填写: '请完整填写',
  序号为: '序号为',
  的必填项: '的必填项',
  为亚马逊: '为亚马逊',
  为私人: '为私人',
  产品编号: '产品编号',
  申报信息总数量: '申报信息总数量',
  审核失败信息: '审核失败信息',
  发货方式: '发货方式',
  未制单编辑点击提交: '未制单编辑点击提交',
  验证申报金额是否超限: '验证申报金额是否超限',
  则需要弹出窗口: '则需要弹出窗口',
  窗口文字信息为接口的: '窗口文字信息为接口的',
  提示: '提示',
  确认制单: '确认制单',
  取消制单: '取消制单',
  生成标签: '生成标签',
  入库签: '入库签',
  入库单: '入库单',
  个: '个',
  列表选中取消: '列表选中取消',
  点击申报信息批量删除: '点击申报信息批量删除',
  请至少勾选一条申报信息数据: '请至少勾选一条申报信息数据',
  保存申报信息数据: '保存申报信息数据',
  计算申报总价值: '计算申报总价值',
  运价试算: '运价试算',
  请至少输入一条箱单信息: '请至少输入一条箱单信息',
  请选择亚马逊仓库: '请选择亚马逊仓库',
  序号: '序号',
  标签号: '标签号',
  箱号: '箱号',
  单箱长: '单箱长',
  单箱宽: '单箱宽',
  单箱高: '单箱高',
  单箱重量: '单箱重量',
  预计体积重: '预计体积重',
  中文品名: '中文品名',
  英文品名: '英文品名',
  产品: '产品',
  申报单价: '申报单价',
  数量: '数量',
  重量: '重量',
  海关编码: '海关编码',
  材质: '材质',
  型号: '型号',
  用途: '用途',
  品牌: '品牌',
  品牌类型: '品牌类型',
  净重: '净重',
  采购单价: '采购单价',
  图片: '图片',
  产品图片链接: '产品图片链接',
  产品销售链接: '产品销售链接',
  制单账号和基础信息: '制单账号和基础信息',
  基础信息: '基础信息',
  产品名称: '产品名称',
  交税模式: '交税模式',
  请选择交税模式: '请选择交税模式',
  包税: '包税',
  自税: '自税',
  递延: '递延',
  请选择是否购买保险: '请选择是否购买保险',
  请填写客户单号: '请填写客户单号',
  客户单号不能超过: '客户单号不能超过',
  位字符: '位字符',
  不能输入: '不能输入',
  开头的单号: '开头的单号',
  其他信息: '其他信息',
  预约信息: '预约信息',
  请选择: '请选择',
  新增揽收地址: '新增揽收地址',
  取消: '取消',
  收货人信息: '收货人信息',
  亚马逊仓库: '亚马逊仓库',
  温馨提示: '温馨提示',
  请核对亚马逊后台地址: '请核对亚马逊后台地址',
  以亚马逊后台地址为准: '以亚马逊后台地址为准',
  仓库代码: '仓库代码',
  请选择仓库代码: '请选择仓库代码',
  仓库代码不能超过: '仓库代码不能超过',
  姓名: '姓名',
  姓名不能超过: '姓名不能超过',
  请输入少于: '请输入少于',
  个字符: '个字符',
  暂时不是必填: '暂时不是必填',
  请输入地址: '请输入地址',
  地址不能超过: '地址不能超过',
  城市: '城市',
  请输入城市: '请输入城市',
  请输入汉字: '请输入汉字',
  城市不能超过: '城市不能超过',
  邮编: '邮编',
  请输入邮编: '请输入邮编',
  邮编不能超过: '邮编不能超过',
  门牌号: '门牌号',
  门牌号不能超过: '门牌号不能超过',
  号: '号',
  公司: '公司',
  公司名不能超过: '公司名不能超过',
  州省: '州省',
  请输入州省: '请输入州省',
  州省不能超过: '州省不能超过',
  电话: '电话',
  电话不能超过: '电话不能超过',
  邮箱: '邮箱',
  请输入正确格式的邮箱: '请输入正确格式的邮箱',
  邮箱不能超过: '邮箱不能超过',
  跟踪单号: '跟踪单号',
  请输入: '请输入',
  请输入英文: '请输入英文',
  数字: '数字',
  跟踪单号过于复杂: '跟踪单号过于复杂',
  位以下字符: '位以下字符',
  输入多个时: '输入多个时',
  请用逗号隔开: '请用逗号隔开',
  请输入姓名: '请输入姓名',
  请输入电话: '请输入电话',
  不能超过: '不能超过',
  下载模板: '下载模板',
  批量上传: '批量上传',
  确认删除吗: '确认删除吗',
  批量删除: '批量删除',
  申报金额: '申报金额',
  用于目的国进口清关: '用于目的国进口清关',
  与不包税税金及丢货赔偿相关: '与不包税税金及丢货赔偿相关',
  丢货赔付会结合客户真实采购发票对比处理: '丢货赔付会结合客户真实采购发票对比处理',
  具体详参价格表赔付规则: '具体详参价格表赔付规则',
  建议申报货值最低按售价的: '建议申报货值最低按售价的',
  填写: '填写',
  申报总价值: '申报总价值',
  是否单独报关: '是否单独报关',
  请上传: '请上传',
  压缩包文件: '压缩包文件',
  该文件不支持预览: '该文件不支持预览',
  上传: '上传',
  期望揽收时间: '期望揽收时间',
  预计自送时间: '预计自送时间',
  备注不能超过: '备注不能超过',
  特殊情况请添加到备注: '特殊情况请添加到备注',
  保存: '保存',
  返回: '返回',
  金额试算: '金额试算',
  预估基础运费: '预估基础运费',
  请以实际账单为准: '请以实际账单为准',
  用户下载标签: '用户下载标签',
  为了您的货物能快速入库: '为了您的货物能快速入库',
  请每箱贴上燕文: '请每箱贴上燕文',
  否则无法入库: '否则无法入库',
  立即下载: '立即下载',
  正在加载中: '正在加载中',
};
